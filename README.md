# AeTrust Fintech Backend

A comprehensive fintech solution providing digital wallets, payment gateways, remittances, merchant services, and more.

## 🚀 Features

### Core Services
- **Digital Wallets** - Multi-currency wallet management
- **Payment Gateway** - Secure payment processing
- **Remittances** - Cross-border money transfers
- **Merchant Services** - Business payment solutions
- **Bill Payments** - Utility and service payments
- **Airtime Top-up** - Mobile credit services

### Security & Authentication
- **JWT Authentication** - Secure token-based auth
- **Multi-Factor Authentication** - OTP via email/SMS
- **KYC Levels** - Tiered verification system
- **Transaction PINs** - Additional security layer
- **Biometric Support** - Modern authentication methods

### Technical Features
- **RESTful APIs** - Clean, documented endpoints
- **Real-time Processing** - Instant transaction handling
- **Multi-currency Support** - Global payment capabilities
- **Audit Trails** - Comprehensive logging
- **Rate Limiting** - API protection
- **Health Monitoring** - System status tracking

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   Database      │
│   (React)       │◄──►│   (Spring Boot) │◄──►│   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Redis Cache   │
                       └─────────────────┘
```

## 🛠️ Technology Stack

- **Backend**: Spring Boot 3.2.1, Java 17
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **Security**: Spring Security, JWT
- **Documentation**: OpenAPI 3, Swagger UI
- **Containerization**: Docker, Docker Compose
- **Build Tool**: Maven

## 📋 Prerequisites

- Java 17+
- Maven 3.8+
- Docker & Docker Compose
- PostgreSQL 15+ (if running locally)
- Redis 7+ (if running locally)

## 🚀 Quick Start

### Using Docker (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd aetrustfintech-backend
   ```

2. **Start all services**
   ```bash
   docker-compose up -d
   ```

3. **Access the application**
   - API: http://localhost:8080
   - Swagger UI: http://localhost:8080/swagger-ui.html
   - Frontend: http://localhost:80

### Local Development

1. **Start PostgreSQL and Redis**
   ```bash
   docker-compose up postgres redis -d
   ```

2. **Run the application**
   ```bash
   mvn spring-boot:run
   ```

## 📚 API Documentation

### Authentication Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/auth/register` | Register new user |
| POST | `/api/auth/login` | User login |
| POST | `/api/auth/verify-otp` | Verify OTP code |
| POST | `/api/auth/resend-otp` | Resend OTP |
| POST | `/api/auth/refresh` | Refresh access token |
| POST | `/api/auth/logout` | User logout |

### User Management Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/users/me` | Get current user profile |
| PUT | `/api/users/profile` | Update user profile |
| PUT | `/api/users/password` | Change password |
| POST | `/api/users/2fa/enable` | Enable 2FA |
| POST | `/api/users/transaction-pin` | Set transaction PIN |

### Example API Calls

#### Register User
```bash
curl -X POST http://localhost:8080/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "password": "SecurePass123!",
    "confirmPassword": "SecurePass123!",
    "firstName": "John",
    "lastName": "Doe",
    "countryCode": "US"
  }'
```

#### Login
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "identifier": "<EMAIL>",
    "password": "SecurePass123!"
  }'
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SPRING_DATASOURCE_URL` | Database URL | `******************************************` |
| `SPRING_DATASOURCE_USERNAME` | Database username | `aetrustuser` |
| `SPRING_DATASOURCE_PASSWORD` | Database password | `aetrustpass123` |
| `JWT_SECRET` | JWT signing secret | `change-this-in-production` |
| `JWT_EXPIRATION` | Access token expiration (ms) | `86400000` (24h) |
| `JWT_REFRESH_EXPIRATION` | Refresh token expiration (ms) | `604800000` (7d) |

### Application Properties

Key configurations in `application.properties`:

```properties
# Database
spring.datasource.url=******************************************
spring.jpa.hibernate.ddl-auto=update

# JWT
jwt.secret=${JWT_SECRET:change-this-in-production}
jwt.expiration=${JWT_EXPIRATION:86400000}

# OTP
otp.expiration=300000
otp.length=6

# KYC Limits
kyc.tier0.daily-limit=100.00
kyc.tier1.daily-limit=1000.00
kyc.tier2.daily-limit=10000.00
kyc.tier3.daily-limit=100000.00
```

## 🧪 Testing

### Run Tests
```bash
mvn test
```

### Test Coverage
```bash
mvn jacoco:report
```

## 📊 Monitoring

### Health Checks
- Application: http://localhost:8080/api/health
- Actuator: http://localhost:8080/actuator/health
- Database: http://localhost:8080/actuator/health/db

### Metrics
- http://localhost:8080/actuator/metrics
- http://localhost:8080/actuator/prometheus

## 🔒 Security

### Authentication Flow
1. User registers with email/phone
2. OTP verification for email/phone
3. Login with credentials
4. JWT tokens issued (access + refresh)
5. Protected endpoints require valid JWT

### KYC Levels
- **Tier 0**: Basic registration - $100 daily limit
- **Tier 1**: Email/Phone verified - $1,000 daily limit
- **Tier 2**: ID verification - $10,000 daily limit
- **Tier 3**: Full KYC - $100,000 daily limit

## 🚀 Deployment

### Production Checklist
- [ ] Change default JWT secret
- [ ] Configure production database
- [ ] Set up SSL certificates
- [ ] Configure email/SMS providers
- [ ] Set up monitoring and logging
- [ ] Configure backup strategies
- [ ] Set up CI/CD pipeline

### Docker Production
```bash
docker-compose -f docker-compose.prod.yml up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is proprietary software owned by AeTrust Fintech.

## 📞 Support

- Email: <EMAIL>
- Documentation: https://docs.aetrust.com
- Issues: Create GitHub issue

---

**AeTrust Fintech Backend** - Powering the future of digital finance 🚀