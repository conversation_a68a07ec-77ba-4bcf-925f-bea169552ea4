version: '3.9'

networks:
  aetrustnet:

services:
  postgres:
    image: postgres:15-alpine
    container_name: aetrust-postgres
    restart: always
    networks:
      - aetrustnet
    environment:
      POSTGRES_DB: aetrustdb
      POSTGRES_USER: aetrustuser
      POSTGRES_PASSWORD: aetrustpass123
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U aetrustuser -d aetrustdb"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: aetrust-redis
    restart: always
    networks:
      - aetrustnet
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  aetrustapi:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: aetrust-backend
    networks:
      - aetrustnet
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *****************************************
      SPRING_DATASOURCE_USERNAME: aetrustuser
      SPRING_DATASOURCE_PASSWORD: aetrustpass123
      SPRING_DATA_REDIS_HOST: redis
      SPRING_DATA_REDIS_PORT: 6379
      JWT_SECRET: aetrust-super-secret-jwt-key-for-production-change-this
      JWT_EXPIRATION: 86400000
      JWT_REFRESH_EXPIRATION: 604800000
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/api/health" ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  frontend:
    build:
      context: ./frontend
      args:
        REACT_APP_API_BASE_URL: /api
      dockerfile: Dockerfile
    container_name: aetrust-frontend
    networks:
      - aetrustnet
    ports:
      - "80:80"
    depends_on:
      aetrustapi:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost" ]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  pgdata:
  redis_data:
