# AeTrust Microservices Implementation Guide
## Step-by-Step Migration from Monolith to Microservices

### Overview
This guide provides the exact order and implementation details for converting the AeTrust monolith into 17 microservices. Each step includes specific deliverables, APIs, and migration strategies.

---

## Phase 1: Infrastructure & Security Foundation (Weeks 1-4)

### Step 1: API Gateway Service
**Priority:** CRITICAL - Must be first to route all traffic
**Timeline:** Week 1

#### Implementation Details
```java
// New Service: aetrust-api-gateway
Technology Stack:
- Spring Cloud Gateway
- Redis (rate limiting)
- JWT validation
- Load balancing
```

#### Key Features
- Request routing to monolith initially
- Rate limiting (100 requests/minute per user)
- JWT token validation
- Request/response logging
- CORS handling

#### APIs to Implement
```
Gateway Routes:
- /api/v1/auth/** → auth-service (later)
- /api/v1/users/** → user-service (later)
- /api/v1/kyc/** → kyc-service (later)
- /api/v1/wallet/** → wallet-service (later)
- /api/v1/** → monolith (initially)
```

#### Migration Strategy
1. Deploy gateway in front of existing monolith
2. Route all traffic through gateway
3. Gradually redirect routes to new microservices

---

### Step 2: Configuration Service
**Priority:** HIGH - Needed for all other services
**Timeline:** Week 1

#### Implementation Details
```java
// New Service: aetrust-config-service
Technology Stack:
- Spring Cloud Config Server
- Git repository for configs
- Encryption for sensitive data
```

#### Key Features
- Centralized configuration management
- Environment-specific configs (dev, staging, prod)
- Encrypted sensitive properties
- Real-time config refresh

#### Configuration Structure
```
config-repo/
├── application.yml (common configs)
├── auth-service.yml
├── kyc-service.yml
├── wallet-service.yml
└── payment-service.yml
```

---

### Step 3: Tokenization Service
**Priority:** CRITICAL - Security requirement
**Timeline:** Week 2

#### Implementation Details
```java
// New Service: aetrust-tokenization-service
Technology Stack:
- Spring Boot
- HashiCorp Vault
- AES-256 encryption
- PostgreSQL (token mapping)
```

#### Key Features
- PII tokenization/detokenization
- Format-preserving encryption
- Audit logging for all operations
- Key rotation support

#### APIs to Implement
```java
POST /api/tokenize
{
    "data": "sensitive_data",
    "dataType": "NATIONAL_ID|PHONE|EMAIL|BANK_ACCOUNT"
}
Response: { "token": "tok_abc123xyz" }

POST /api/detokenize
{
    "token": "tok_abc123xyz"
}
Response: { "data": "sensitive_data" }

POST /api/tokenize/bulk
{
    "items": [
        {"data": "data1", "dataType": "NATIONAL_ID"},
        {"data": "data2", "dataType": "PHONE"}
    ]
}
```

#### Migration Strategy
1. Deploy tokenization service
2. Create migration script for existing PII data
3. Update monolith to use tokenization APIs
4. Migrate all existing sensitive data

---

### Step 4: Audit & Observability Service
**Priority:** HIGH - Compliance requirement
**Timeline:** Week 2

#### Implementation Details
```java
// New Service: aetrust-audit-service
Technology Stack:
- Spring Boot
- Elasticsearch (log storage)
- Kafka (event streaming)
- Kibana (dashboards)
```

#### Key Features
- Immutable audit logs
- Real-time log aggregation
- Compliance reporting
- Performance monitoring

#### APIs to Implement
```java
POST /api/audit/log
{
    "eventType": "USER_LOGIN|TRANSACTION|KYC_UPDATE",
    "userId": "user-id",
    "details": {},
    "timestamp": "2025-01-09T10:00:00Z"
}

GET /api/audit/trail/{entityId}
Response: [audit events]

POST /api/audit/search
{
    "eventType": "TRANSACTION",
    "dateFrom": "2025-01-01",
    "dateTo": "2025-01-31"
}
```

---

## Phase 2: Core Financial Services (Weeks 3-6)

### Step 5: Authentication & IAM Service
**Priority:** CRITICAL - Extract from monolith first
**Timeline:** Week 3

#### Implementation Details
```java
// New Service: aetrust-auth-service
Technology Stack:
- Spring Boot
- Spring Security
- Redis (sessions)
- PostgreSQL (users)
```

#### Key Features
- JWT token management
- 2FA implementation
- Session management
- Role-based access control

#### APIs to Implement
```java
POST /api/auth/login
{
    "identifier": "email_or_phone",
    "password": "password",
    "deviceInfo": "device_details"
}

POST /api/auth/verify-2fa
{
    "token": "temp_token",
    "otpCode": "123456"
}

POST /api/auth/refresh
{
    "refreshToken": "refresh_token"
}

GET /api/auth/permissions/{userId}
Response: ["WALLET_READ", "WALLET_WRITE", "ADMIN_ACCESS"]
```

#### Migration Strategy
1. Extract AuthService from monolith
2. Move User entity and UserRepository
3. Update API Gateway to route auth requests
4. Test authentication flow end-to-end

---

### Step 6: Identity & KYC Service
**Priority:** CRITICAL - Core business logic
**Timeline:** Week 4

#### Implementation Details
```java
// New Service: aetrust-kyc-service
Technology Stack:
- Spring Boot
- PostgreSQL (KYC profiles)
- S3/Cloudinary (documents)
- External APIs (NIDA)
```

#### Key Features
- Country-specific KYC workflows
- Document verification
- Tier management
- NIDA integration (Rwanda)

#### APIs to Implement
```java
POST /api/kyc/profile
{
    "userId": "user-id",
    "country": "ETHIOPIA|RWANDA",
    "kycLevel": "LEVEL_1|TIER_I",
    "documents": ["doc1", "doc2"]
}

GET /api/kyc/profile/{userId}
Response: KYC profile with tier info

PUT /api/kyc/upgrade
{
    "userId": "user-id",
    "targetLevel": "LEVEL_2",
    "additionalDocuments": []
}

POST /api/kyc/verify-nida
{
    "nidaNumber": "*********0123456",
    "country": "RWANDA"
}
```

#### Migration Strategy
1. Extract KYC services from monolith
2. Move KYC entities and repositories
3. Integrate with tokenization service
4. Update compliance validations

---

### Step 7: Wallet & Ledger Service
**Priority:** CRITICAL - Core financial engine
**Timeline:** Week 5

#### Implementation Details
```java
// New Service: aetrust-wallet-service
Technology Stack:
- Spring Boot
- PostgreSQL (double-entry ledger)
- Redis (balance caching)
- Kafka (transaction events)
```

#### Key Features
- Double-entry accounting
- Real-time balance updates
- Transaction limits enforcement
- Account tier validation

#### APIs to Implement
```java
POST /api/wallet/create
{
    "userId": "user-id",
    "accountType": "USER|AGENT|MERCHANT",
    "currency": "ETB|RWF"
}

GET /api/wallet/balance/{accountId}
Response: { "balance": 1000.00, "currency": "ETB" }

POST /api/wallet/transfer
{
    "fromAccountId": "account-1",
    "toAccountId": "account-2",
    "amount": 100.00,
    "currency": "ETB",
    "description": "P2P transfer"
}

GET /api/wallet/history/{accountId}
Response: [transaction history]

POST /api/wallet/validate-limits
{
    "accountId": "account-1",
    "amount": 100.00,
    "transactionType": "DEBIT"
}
```

#### Database Schema
```sql
-- Accounts table
CREATE TABLE accounts (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    account_type VARCHAR(20) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    balance DECIMAL(15,2) DEFAULT 0.00,
    tier_level VARCHAR(20),
    daily_limit DECIMAL(15,2),
    monthly_limit DECIMAL(15,2),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Ledger entries (double-entry)
CREATE TABLE ledger_entries (
    id UUID PRIMARY KEY,
    transaction_id UUID NOT NULL,
    account_id UUID NOT NULL,
    entry_type VARCHAR(10) NOT NULL, -- DEBIT/CREDIT
    amount DECIMAL(15,2) NOT NULL,
    balance_after DECIMAL(15,2) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

---

### Step 8: Float Management Service
**Priority:** CRITICAL - Regulatory compliance
**Timeline:** Week 6

#### Implementation Details
```java
// New Service: aetrust-float-service
Technology Stack:
- Spring Boot
- PostgreSQL (reconciliation data)
- Bank APIs
- Scheduled jobs
```

#### Key Features
- Daily bank reconciliation
- Automated reporting to NBE/BNR
- Discrepancy detection and alerting
- Multi-bank support (Rwanda)

#### APIs to Implement
```java
GET /api/float/balance
Response: {
    "totalFloat": 1000000.00,
    "bankBalance": 1000000.00,
    "discrepancy": 0.00,
    "lastReconciliation": "2025-01-09T10:00:00Z"
}

POST /api/float/reconcile
{
    "date": "2025-01-09",
    "bankBalances": [
        {"bank": "CBE", "balance": 500000.00},
        {"bank": "BOA", "balance": 500000.00}
    ]
}

GET /api/float/reports/{country}
Response: Regulatory report data

POST /api/float/alert
{
    "type": "DISCREPANCY",
    "amount": 1000.00,
    "description": "Float mismatch detected"
}
```

---

## Phase 3: Payment & Compliance Services (Weeks 7-10)

### Step 9: Payment & Remittance Engine
**Priority:** HIGH - Core business functionality
**Timeline:** Week 7

#### Implementation Details
```java
// New Service: aetrust-payment-service
Technology Stack:
- Spring Boot
- PostgreSQL (transactions)
- Kafka (events)
- External APIs (MTN MoMo, Onafriq)
```

#### Key Features
- Payment routing and processing
- PSP integrations
- FX conversion
- Settlement reconciliation

#### APIs to Implement
```java
POST /api/payments/process
{
    "fromAccountId": "account-1",
    "toAccountId": "account-2",
    "amount": 100.00,
    "currency": "ETB",
    "paymentMethod": "WALLET|MOBILE_MONEY|BANK",
    "description": "Payment description"
}

POST /api/payments/mobile-money
{
    "provider": "MTN_MOMO",
    "phoneNumber": "+************",
    "amount": 100.00,
    "currency": "ETB"
}

GET /api/payments/status/{transactionId}
Response: {
    "status": "PENDING|COMPLETED|FAILED",
    "transactionId": "txn-123",
    "amount": 100.00
}

POST /api/remittance/send
{
    "fromCountry": "ETHIOPIA",
    "toCountry": "RWANDA",
    "amount": 100.00,
    "fromCurrency": "ETB",
    "toCurrency": "RWF",
    "recipient": "recipient-details"
}
```

---

### Step 10: Compliance & AML Service
**Priority:** HIGH - Regulatory requirement
**Timeline:** Week 8

#### Implementation Details
```java
// New Service: aetrust-compliance-service
Technology Stack:
- Spring Boot
- PostgreSQL (compliance data)
- Kafka (real-time screening)
- External APIs (sanctions lists)
```

#### Key Features
- Real-time transaction screening
- AML/CFT monitoring
- Sanctions list checking
- Automated SAR generation

#### APIs to Implement
```java
POST /api/compliance/screen
{
    "transactionId": "txn-123",
    "userId": "user-id",
    "amount": 100.00,
    "currency": "ETB",
    "transactionType": "P2P_TRANSFER"
}

GET /api/compliance/alerts
Response: [compliance alerts]

POST /api/compliance/report-suspicious
{
    "userId": "user-id",
    "transactionIds": ["txn-1", "txn-2"],
    "reason": "UNUSUAL_PATTERN",
    "description": "Multiple large transactions"
}

GET /api/compliance/rules/{country}
Response: [compliance rules for country]
```

---

### Step 11: Notification Service
**Priority:** MEDIUM - User experience
**Timeline:** Week 9

#### Implementation Details
```java
// New Service: aetrust-notification-service
Technology Stack:
- Spring Boot
- Kafka (event consumption)
- Redis (templates)
- External APIs (SMS, Email, WhatsApp)
```

#### Key Features
- Multi-channel notifications
- Template management
- Delivery tracking
- Localization support

#### APIs to Implement
```java
POST /api/notifications/send
{
    "userId": "user-id",
    "type": "SMS|EMAIL|PUSH|WHATSAPP",
    "template": "TRANSACTION_SUCCESS",
    "variables": {
        "amount": "100.00",
        "currency": "ETB"
    }
}

POST /api/notifications/bulk
{
    "userIds": ["user-1", "user-2"],
    "type": "SMS",
    "message": "System maintenance notification"
}

GET /api/notifications/status/{notificationId}
Response: {
    "status": "SENT|DELIVERED|FAILED",
    "deliveredAt": "2025-01-09T10:00:00Z"
}
```

---

### Step 12: User Management Service
**Priority:** MEDIUM - Extract remaining user logic
**Timeline:** Week 10

#### Implementation Details
```java
// New Service: aetrust-user-service
Technology Stack:
- Spring Boot
- PostgreSQL (user profiles)
- Redis (caching)
```

#### Key Features
- User profile management
- Registration workflows
- Account status management
- User preferences

#### APIs to Implement
```java
POST /api/users/register
{
    "email": "<EMAIL>",
    "phone": "+************",
    "firstName": "John",
    "lastName": "Doe",
    "country": "ETHIOPIA"
}

GET /api/users/profile/{userId}
Response: User profile data

PUT /api/users/profile/{userId}
{
    "firstName": "Updated Name",
    "preferences": {
        "language": "en",
        "notifications": true
    }
}

DELETE /api/users/{userId}
// Soft delete with compliance requirements
```

---

## Phase 4: Business Services (Weeks 11-14)

### Step 13: Agent Network Service
**Priority:** MEDIUM - Business expansion
**Timeline:** Week 11

#### Implementation Details
```java
// New Service: aetrust-agent-service
Technology Stack:
- Spring Boot
- PostgreSQL (agent data)
- Redis (location caching)
- Maps API (geolocation)
```

#### Key Features
- Agent onboarding and KYC
- Cash-in/cash-out operations
- Commission management
- Performance tracking

#### APIs to Implement
```java
POST /api/agents/onboard
{
    "personalInfo": {},
    "businessInfo": {},
    "location": {
        "latitude": 9.0192,
        "longitude": 38.7525
    }
}

POST /api/agents/cash-in
{
    "agentId": "agent-123",
    "customerId": "user-456",
    "amount": 100.00,
    "currency": "ETB"
}

POST /api/agents/cash-out
{
    "agentId": "agent-123",
    "customerId": "user-456",
    "amount": 50.00,
    "currency": "ETB"
}

GET /api/agents/nearby
{
    "latitude": 9.0192,
    "longitude": 38.7525,
    "radius": 5000
}
Response: [nearby agents]
```

---

### Step 14: Merchant Service
**Priority:** MEDIUM - Revenue generation
**Timeline:** Week 12

#### Implementation Details
```java
// New Service: aetrust-merchant-service
Technology Stack:
- Spring Boot
- PostgreSQL (merchant data)
- QR code generation
- Payment processing integration
```

#### Key Features
- Merchant onboarding (KYB)
- Payment acceptance
- QR code generation
- Settlement management

#### APIs to Implement
```java
POST /api/merchants/onboard
{
    "businessInfo": {},
    "ownerInfo": {},
    "businessLicense": "license-number",
    "expectedVolume": 10000.00
}

POST /api/merchants/generate-qr
{
    "merchantId": "merchant-123",
    "amount": 25.00,
    "currency": "ETB",
    "description": "Coffee purchase"
}

POST /api/merchants/accept-payment
{
    "merchantId": "merchant-123",
    "customerId": "user-456",
    "amount": 25.00,
    "paymentMethod": "QR_SCAN"
}

GET /api/merchants/settlements/{merchantId}
Response: [settlement history]
```

---

### Step 15: Bill Payment Service
**Priority:** MEDIUM - User convenience
**Timeline:** Week 13

#### Implementation Details
```java
// New Service: aetrust-bill-service
Technology Stack:
- Spring Boot
- PostgreSQL (bill data)
- External APIs (utility companies)
- Scheduling (recurring payments)
```

#### Key Features
- Biller integrations
- Bill inquiry and payment
- Recurring payments
- Receipt generation

#### APIs to Implement
```java
GET /api/bills/billers
Response: [available billers by category]

POST /api/bills/inquiry
{
    "billerId": "EEPCO",
    "accountNumber": "*********",
    "customerInfo": {}
}

POST /api/bills/pay
{
    "billerId": "EEPCO",
    "accountNumber": "*********",
    "amount": 150.00,
    "currency": "ETB"
}

POST /api/bills/schedule
{
    "billerId": "EEPCO",
    "accountNumber": "*********",
    "frequency": "MONTHLY",
    "maxAmount": 200.00
}
```

---

### Step 16: Digital Lending Service
**Priority:** LOW - Advanced feature
**Timeline:** Week 14

#### Implementation Details
```java
// New Service: aetrust-lending-service
Technology Stack:
- Spring Boot
- PostgreSQL (loan data)
- ML models (credit scoring)
- Kafka (loan events)
```

#### Key Features
- Loan application processing
- Credit scoring
- Disbursement automation
- Repayment tracking

#### APIs to Implement
```java
POST /api/loans/apply
{
    "userId": "user-123",
    "loanType": "MICRO|SME|BNPL",
    "amount": 5000.00,
    "currency": "ETB",
    "purpose": "Business expansion"
}

GET /api/loans/eligibility/{userId}
Response: {
    "eligible": true,
    "maxAmount": 10000.00,
    "interestRate": 15.5
}

POST /api/loans/disburse
{
    "loanId": "loan-123",
    "accountId": "account-456"
}

POST /api/loans/repay
{
    "loanId": "loan-123",
    "amount": 500.00,
    "paymentMethod": "WALLET"
}
```

---

## Phase 5: Advanced Services (Weeks 15-16)

### Step 17: Savings & Investment Service
**Priority:** LOW - Value-added feature
**Timeline:** Week 15

#### Implementation Details
```java
// New Service: aetrust-savings-service
Technology Stack:
- Spring Boot
- PostgreSQL (savings data)
- Scheduled jobs (interest calculation)
- Investment APIs
```

#### Key Features
- Savings account management
- Interest calculation
- Goal-based savings
- Investment products

#### APIs to Implement
```java
POST /api/savings/create
{
    "userId": "user-123",
    "savingsType": "REGULAR|GOAL_BASED|GROUP",
    "targetAmount": 10000.00,
    "targetDate": "2025-12-31"
}

POST /api/savings/deposit
{
    "savingsId": "savings-123",
    "amount": 100.00,
    "fromAccountId": "account-456"
}

GET /api/savings/interest/{savingsId}
Response: {
    "currentBalance": 1050.00,
    "interestEarned": 50.00,
    "interestRate": 5.0
}

POST /api/investments/buy
{
    "userId": "user-123",
    "productId": "bond-001",
    "amount": 1000.00
}
```

---

## Migration Strategy & Best Practices

### Database Migration
1. **Database per Service:** Each microservice gets its own database
2. **Data Migration Scripts:** Automated scripts to move data from monolith
3. **Dual Write Pattern:** Write to both old and new systems during transition
4. **Event Sourcing:** Use events to keep systems in sync

### Service Communication
1. **Synchronous:** REST APIs for real-time operations
2. **Asynchronous:** Kafka events for eventual consistency
3. **Circuit Breakers:** Prevent cascade failures
4. **Retry Logic:** Exponential backoff for failed requests

### Testing Strategy
1. **Unit Tests:** Each service independently tested
2. **Integration Tests:** Service-to-service communication
3. **Contract Tests:** API contract validation
4. **End-to-End Tests:** Complete user journey testing

### Deployment Strategy
1. **Blue-Green Deployment:** Zero-downtime deployments
2. **Feature Flags:** Gradual feature rollout
3. **Monitoring:** Comprehensive observability
4. **Rollback Plans:** Quick rollback procedures

---

## Success Metrics

### Technical Metrics
- **Service Uptime:** 99.9% per service
- **Response Time:** <200ms for critical operations
- **Error Rate:** <0.1% for financial transactions
- **Deployment Frequency:** Daily deployments

### Business Metrics
- **Transaction Success Rate:** >99.5%
- **User Onboarding Time:** <5 minutes
- **Compliance Score:** 100% regulatory adherence
- **Customer Satisfaction:** >4.5/5 rating

This step-by-step guide provides the exact roadmap for converting your monolith into a fully functional microservices architecture. Each step builds upon the previous one, ensuring a smooth transition while maintaining system stability and compliance.
