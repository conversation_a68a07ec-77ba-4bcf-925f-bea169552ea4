[AeTrust Input] New document title is proposed:
System Architecture and Design Specifications

[AeTrust Input] One paragraph outlines the documentation:
This document outlines .......

1. Project Overview
The solution will offer digital wallets,paymentgatway, payments, remittances, merchant
services, digital lending, and integrations.
Key Platform Capabilities:
• A secure, extensible API gateway to standardize integration with third-party services
(PSPs, banks, telcos, CRBs)
• Real-time monitoring, alerting, and log tracing via integrated DevSecOps tools
• End-user access channels including Android/iOS apps, web portals, USSD, and
WhatsApp
• A unified admin console for system-wide oversight: transaction logs, fraud detection, SLA
metrics
• Multi-country deployments with currency conversion, localization, and compliance plugins
• Secure data handling aligned with PCI-DSS, GDPR, NDPR, and national laws
• Flexible ledger and settlement engine (wallet, card, mobile money, crypto, BNPL)
• Containerized backup, service failover, horizontal autoscaling
• Scalable for future add-ons (DeFi, crowdfunding, AI-powered credit scoring)
2. Scope & Functional Coverage
2.1 Core Functional Modules
A. User Account & Identity
• Mobile registration (OTP, eKYC)
• Biometric login (Face ID, Touch ID)
• Multi-language, accessibility features
• Tiered KYC and profile management
B. Wallet Operations
• Multi-tiered accounts (users, SMEs, merchants, agents), each with configurable permissions
and access roles
• Wallet-to-wallet transfers within the ecosystem, supporting P2P, P2B, and agent-assisted
transfers
• Real-time wallet balance updates and transaction history
• Bank account and mobile money linkage for deposits and withdrawals
• Virtual account numbers for each user profile to enable unique identity and auto-routing of
incoming funds
• Linked prepaid or debit card management for in-app and offline purchases
• Transaction PIN, biometric authentication, and time-bound OTP confirmation for wallet activity
• Dispute resolution workflow for wallet-based transactions
• Daily and monthly wallet limits configurable by KYC level and user type
• Integration-ready for escrow and conditional wallets for marketplaces and B2B payments
• Full API documentation for wallet creation, fund movement, statements, and balance inquiries
• Load funds (bank, card, agent, mobile money)
2
• Withdraw (agent, bank, USSD, ATM cardless)
• P2P transfers (via phone, QR, alias)
• Tontine/chama-style contributions
• Multi-currency wallet support
C. Payments & Merchant Services
• Integration with multiple billers across telecoms, utilities (electricity, water, gas),
government services (taxes, licenses), education (school fees), and cable/TV services
• Airtime top-up and data bundle purchases with dynamic operator selection (MTN, Airtel,
Vodacom, etc.)
• Real-time bill inquiry and confirmation APIs for supported service providers
• QR code bill scanning and instant bill payment initiation
• Scheduled payments and recurring billing (weekly, monthly, custom intervals)
• Support for over-the-counter (OTC) bill payments by agents on behalf of users
• Notification engine for due bill alerts via SMS, email, and in-app push
• In-app bill management history with downloadable receipts and invoice records
• Option for bill splitting and shared payment requests (e.g., roommates or shared
offices)
• Role-based access for businesses to pay bulk utility bills across locations
• API gateway for 3rd-party billers to onboard and integrate
• Dashboard tracking of bill payment volumes, trends, and failures
• Support for voucher codes, discount integrations, and loyalty-based rewards
• QR code generation/scanning (static/dynamic)
• POS mode for QR and NFC
• Bill payments, airtime, data, transport
• NFC Tap-to-pay and Apple Pay integration
D. Lending & Credit
• Microloans, BNPL, salary advances
• AI-powered credit scoring
• Repayment tracking and reminders
E. Savings & Investment
• Savings vaults and cooperative pools
• Interest products, micro-investments
2.2 Agent & Ecosystem Integrations
A. Agent Network Portal
• KYC onboarding
• Cash in/out services
• Float and commission management
• Geo-tagged locator system
2.3 Payment Gateway & Aggregation
• Real-time routing engine with failover, prioritization
3
• Aggregate mobile money, bank, card into single API
• Support for ISO 8583, ISO 20022, REST, WebSocket
• Dynamic QR generation and JS SDKs
• Merchant onboarding API, reconciliation engine
• PCI-DSS tools: tokenization, audit logs
• Admin analytics and fraud dashboard
2.4 Bill Payments
• Biller integrations across sectors (utilities, gov, education)
• QR scan, recurring payments, bulk bills
• OTC bill pay by agents, receipts, and shared payments
• Dashboard tracking and voucher redemption
2.5 Domestic & Cross-Border Remittance
• Real-time domestic and international P2P remittance support
• FX rate engine with dynamic rate fetching, markup configuration, and margin
management
• Integration with SWIFT, Visa Direct, Mastercard Send, and regional switches such as
PAPSS, COMESA RSP, and SADC RTGS
• Full support for inbound and outbound corridors, including sender verification and
KYC/AML screening
• Real-time and scheduled remittance options with proof-of-delivery notifications
• Country-specific compliance flows (e.g., transaction thresholds, reporting mandates,
source-of-funds)
• API gateway for partners to plug in as sender or receiver institutions
• Blockchain-ready option for crypto rail integration (USDC, BTC, stablecoins) and smart
contract-based disbursement
• Multi-channel cash-out support: wallet, bank, mobile money, and agent locations
• Bulk remittance tools for B2B cross-border payroll or aid disbursement
• Recipient SMS/WhatsApp alerts with transaction code and claim instructions
• Built-in reconciliation engine and cross-border settlement scheduler
• Admin dashboard for corridor performance, compliance reporting, and dispute handling
• Corridor compliance with PAPSS, SADC, SWIFT
• Multi-channel payout (wallet, bank, agents)
• Blockchain-ready (USDC, BTC, stablecoins)
• Bulk B2B tools and partner APIs
2.6 Merchant Services
• End-to-end merchant onboarding and verification process (automated KYC/KYB checks)
• Merchant roles and permission levels (admin, cashier, viewer)
• Merchant wallet with wallet-to-bank and wallet-to-mobile money settlement options
• Transaction dashboard: daily summaries, advanced filters, and downloadable statements
• Bulk payment and disbursement features (salaries, vendor payments, affiliate commissions)
• QR code generation (static/dynamic) for merchant storefronts
• Integration-ready merchant APIs and SDKs for checkout, invoice generation, and transaction
callbacks
4
• Customizable pricing tiers and commission models (flat fee, percentage, volume-based)
• Invoicing tools with auto-generated invoice IDs, tax calculations, and due date tracking
• Real-time settlement tracker with configurable T+1 or instant options
• Dispute resolution module linked with payment and refund APIs
• Merchant settlement reports compatible with accounting systems (CSV, XML, or API sync)
• Merchant classification system (bronze, silver, gold) based on volume, compliance, and ratings
• Loyalty and cashback integration options for merchant customers
• Marketplace-ready merchant tools: inventory management, revenue analytics, customer
support chat plugin
• Admin monitoring of merchant activity, transaction anomalies, and chargeback trends
• Automated onboarding and KYC
• Wallet with settlement features
• Dashboards, invoicing, dispute tools
• Loyalty/cashback, QR integration
• Tiered merchant classification
2.7 Digital Lending
• CRB and alternative credit scoring
• Compliance tracking, borrower analytics
• Support for multiple loan products: nano loans, microloans, SME working capital,
invoice financing, and Buy Now Pay Later (BNPL) options
• Customizable loan product configuration: interest rates, duration, grace period,
repayment type (equal installments, bullet repayment, etc.)
• Digital loan origination via mobile app, web portal, or assisted agent onboarding
• KYC-compliant onboarding with document upload and identity verification
• Credit scoring engine supporting traditional (CRB) and alternative data sources (wallet
activity, mobile usage, transaction patterns)
• Pre-approval and instant loan eligibility display
• Real-time loan offer generation and contract signing (PDF generation + digital consent)
• Disbursement engine: direct to wallet, bank, or mobile money
• Automated repayment scheduling: daily, weekly, monthly options with configurable
grace periods
• Auto-deduction from wallet or linked payment method
• Loan structuring, repayment, collections, workflow and early repayment support
• Loan origination and disbursement, performance tracking: real-time analytics,
repayment trends, NPL alerts
• Collections module with segmented workflows (soft reminders, escalation, legal
handoff)
• Integration-ready with national Credit Reference Bureaus (CRBs) and alternative data
registries
• Admin view with borrower list, repayment reports, and audit logs
• Full audit trail for compliance and financial reporting
• API documentation for all core lending functions (apply, disburse, repay, reconcile)
•
5
2.8 Admin Portal
• Access control, transaction monitoring, reporting templates, fraud alerts
• KYC verification and transaction tracking
• Tiered access and audit logging
3. Security & Compliance
• PCI-DSS compliant architecture
• Card tokenization and encryption
• Device binding, fraud analytics
• 2FA and audit logs
• AML/CFT flows and full traceability
4. Backend & Admin Dashboard
• Role-based access control (RBAC)
• Transaction, wallet, refund, reversal control
• Ledger and audit trail
• Notification engine (SMS, email, push, WhatsApp)
• Dispute management
• Usage heatmaps and reporting templates
5. Integration APIs
• Mobile money (MTN, Airtel, M-Pesa)
• Banking APIs (Open Banking, ISO 20022)
• Biller APIs and education systems
• National ID/eKYC
• CRB and payment gateway APIs
• USSD and SMS Gateway APIs
6. UX/UI Features
• Responsive Material 3/HIG design
• Dark mode, multilingual interface
• Custom themes and app tours
• WCAG-compliant accessibility
7. Support & Notifications
• Live chat, help center
• Push, SMS, email alerts
• AI assistant/chatbot (optional)
8. Value-Added Services (Optional)
• Micro-insurance
• International remittance
• Cashback and reward marketplace
• Voucher systems and loyalty tools
• Cross-border transactions with compliance
6
9. Deliverables
# Deliverable Description
1 Technical Spec System architecture, API contracts, diagrams, DevSecOps.
Delivered with walkthrough.
2 Wallet Module Multi-tier wallets, P2P, escrow, biometric auth, KYC limits, API
suite.
3 Payments Engine Card/mobile/bank routing, QR codes, reconciliation, FX, PSP
APIs.
4 Bill Pay Module Biller integrations, recurring billing, vouchers, bulk payments,
agent OTC.
5 Remittance & FX Corridor rules, FX engine, bulk disbursements, partner APIs,
crypto-ready.
6 Merchant Tools Wallets, QR, invoices, dashboards, settlement tiers, SDKs.
7 Lending Engine Loan origination, scoring, repayment, collections, reporting.
8
Admin + Merchant
Dashboards Control panels, analytics, earnings, activity logs.
9 Android App Full-featured wallet app (Flutter/React Native). Localized,
responsive.
10 Documentation Developer manuals, environment scripts, SOPs, Git repos.
11 UAT & Training Staging tests, bug fixes, admin onboarding, checklist for go-live.
All modules align with ISO 25010 software quality metrics: functionality, usability, reliability,
maintainability, security, and interoperability.
10. Implementation Timeline (6 Weeks)
Week Milestone
1 Kickoff, Technical Specification, Architecture Design
2 Wallet + Payments Microservices Development
3 Lending + Bill Pay Modules; Frontend Prototypes
4 Admin Dashboard, Merchant Tools, Mobile App Finalization
5 UAT, Training, Feedback, QA Fixes
6 Final Handover, Live Deployment Support
11. Notes
This is an MVP implementation. iOS App, USSD channel, blockchain escrow, full AI scoring,
analytics dashboards, and white-labeling modules will be scoped and delivered in subsequent
phases.  International Standards for Fintech Compliance
We start with key international standards relevant to fintech operations, such as digital wallets, payments, remittances, lending, and merchant services. These standards ensure data security, privacy, and integrity while mitigating risks like fraud and money laundering.

Based on established frameworks, here there is a mapped core requirements of each standard to recommended technologies and implementations. These mappings draw from the solution's described capabilities (e.g., PCI-DSS-compliant architecture, tokenization, encryption, AML/CFT flows) and align with technical best practices. Implementations should be integrated into the platform's API gateway, admin console, and security modules for multi-country deployments in regions like Ethiopia and Rwanda.

The standards covered here are:

-	PCI-DSS (Payment Card Industry Data Security Standard): Focuses on protecting cardholder data.
-	GDPR (General Data Protection Regulation): Emphasizes personal data privacy and user rights.
-	ISO 27001: Provides a framework for information security management systems (ISMS).
-	AML/CFT (Anti-Money Laundering / Countering the Financing of Terrorism): Targets financial crime prevention.

Other standards like ISO 20022 (for payment messaging interoperability) or PSD2 (EU-specific open banking) are noted but not detailed here, as they are more operational than core compliance-focused for global fintech. If needed, ISO 20022 can be implemented via standardized APIs for cross-border remittances (e.g., integrating with PAPSS or SWIFT).

1. PCI-DSS Compliance
PCI-DSS ensures secure handling of payment card data, aligning with the solution's payment gateway, tokenization, and audit logs. Version 4.0.1 (effective 2025) emphasizes customized controls and strong cryptography.

Requirement	Technology/Implementation
Build and maintain a secure network (e.g., firewalls to protect cardholder data environment).	Network firewalls (e.g., Cisco ASA or cloud-native like AWS WAF); segment networks using VLANs or micro-segmentation tools like VMware NSX. Integrate with the solution's API gateway for third-party PSP integrations.
Protect stored cardholder data (e.g., do not store full PAN unless necessary).	Data encryption at rest (AES-256 via tools like AWS KMS or HashiCorp Vault); payment card tokenization (e.g., using Vault or Braintree Tokenization Service) to replace sensitive data with tokens in wallets and transactions.
Encrypt transmission of cardholder data over open networks.	TLS 1.3 encryption for all API calls and transmissions; implement HSM (Hardware Security Modules) for key management in payment engines.
Maintain vulnerability management (e.g., anti-virus and regular updates).	Endpoint protection platforms (e.g., CrowdStrike or Microsoft Defender); automated vulnerability scanners like Nessus for containerized backups and autoscaling.
Implement strong access controls (e.g., unique IDs, restrict access by need-to-know).	Role-Based Access Control (RBAC) via tools like Okta or Azure AD; integrate with biometric authentication (Face ID/Touch ID) for admin and user portals.
Regularly monitor and test networks (e.g., logging, intrusion detection).	SIEM (Security Information and Event Management) tools like Splunk or ELK Stack for real-time monitoring, alerting, and fraud analytics; conduct quarterly penetration testing with tools like Burp Suite.
Maintain an information security policy.	Policy management platforms (e.g., Vanta or Drata) for documentation; embed in DevSecOps tools for automated compliance checks during deployments.
2. GDPR Compliance
GDPR protects personal data (e.g., user profiles, transaction history), requiring privacy by design in features like eKYC, multi-currency wallets, and lending scoring. It applies extraterritorially if serving EU users or processing EU data.

Requirement	Technology/Implementation
Lawful basis for processing (e.g., obtain explicit consent).	Consent management platforms (e.g., OneTrust or Usercentrics) integrated into mobile registration and biometric login; use digital consent forms with audit trails for loan origination.
Data minimization and purpose limitation (e.g., collect only necessary data).	Data mapping tools (e.g., Collibra) to identify and limit data in wallets and remittances; implement pseudonymization in AI credit scoring to anonymize non-essential identifiers.
Integrity and confidentiality (e.g., protect against breaches).	End-to-end encryption (e.g., Signal Protocol for WhatsApp integrations); DLP tools like Symantec DLP to prevent unauthorized data exfiltration in merchant services.
Data subject rights (e.g., right to access, erasure – "right to be forgotten").	Automated DSAR (Data Subject Access Request) tools (e.g., TrustArc); integrate with admin console for profile management and deletion workflows in user accounts.
Accountability and breach notification (e.g., notify authorities within 72 hours).	Incident response platforms (e.g., PagerDuty with GDPR templates); appoint a DPO and use logging in notification engines for traceability.
Privacy by design and DPIA (Data Protection Impact Assessment).	Embed in development with tools like PrivacyTech; conduct DPIAs for high-risk features like blockchain escrow or cross-border remittances.
3. ISO 27001 Compliance
This standard establishes an ISMS for overall security, complementing the solution's DevSecOps tools, RBAC, and audit trails.

Requirement	Technology/Implementation
Risk assessment and treatment (e.g., identify threats to assets).	Risk management software (e.g., RiskWatch or RSA Archer); integrate with fraud detection in the unified admin console for ongoing wallet and transaction risks.
Access control (e.g., manage user privileges).	Identity and Access Management (IAM) systems like Okta; apply RBAC to multi-tiered accounts (users, merchants, agents).
Cryptography (e.g., secure data in transit and at rest).	Key management services (e.g., AWS KMS); align with device binding and 2FA in mobile apps.
Incident management (e.g., detect and respond to security events).	SIEM tools (e.g., Splunk) for real-time alerting; link to dispute resolution workflows.
Compliance and continual improvement (e.g., audits and reviews).	Compliance automation platforms (e.g., Vanta); use for ISO 25010 alignment in deliverables like technical specs and UAT.
4. AML/CFT Compliance
AML/CFT prevents illicit finance, integrating with KYC onboarding, transaction monitoring, and CRB integrations in the solution.

Requirement	Technology/Implementation
Customer Due Diligence (CDD/KYC) (e.g., verify identities).	eKYC platforms (e.g., Jumio or Onfido) with biometrics; tiered KYC limits in user accounts and agent portals.
Ongoing transaction monitoring (e.g., detect suspicious patterns).	AI/ML-based monitoring (e.g., ComplyAdvantage or ThetaRay); integrate with real-time FX engine and remittance corridors for anomaly detection.
Sanctions and PEP screening (e.g., check against watchlists).	Screening tools (e.g., Refinitiv World-Check); apply during onboarding and bulk disbursements.
Suspicious Activity Reporting (SAR) and record-keeping.	RegTech platforms (e.g., NICE Actimize); ensure full traceability in ledger and audit trails, with 5-10 year retention.
Risk-based approach (e.g., higher scrutiny for high-risk users).	Risk scoring engines (e.g., integrated with AI credit scoring); customize for cross-border payouts and crypto options.

These mappings provide a technical foundation for compliance. Consultants should prioritize integrations in the 6-week timeline (e.g., security in Weeks 1-2 architecture design). Next steps can include local regulations for Ethiopia (e.g., NBE Directive on Payment Systems) and Rwanda (e.g., NBR Payment System Regulations), mapping similar technologies while adapting for agent banking and payment instruments.

1. Overview of the System Design
The Platform is a modular, multi-channel financial services system that enables merchant
acquiring, P2P remittances, bill payments, agent cash-in/cash-out, digital lending, savings &
investments, and value-added services.
The design follows a layered microservices architecture, exposed via an API Gateway,
supporting USSD, Mobile Apps (iOS/Android), and Web Applications. Core services include
Identity & KYC, Wallet & Ledger, Payments/Remittance Engine, Lending, Savings, and an
Admin/Compliance Portal.
The platform is engineered for regulatory compliance in Rwanda and Ethiopia, with special
emphasis on data localization in Ethiopia and GDPR aligned controls in Rwanda.

[AeTrust Input] A list of key features and benefits in one or two lines:
	Key Features 1
	Benefits
	Key Features 2
	Benefits
	Key Features …..
	Benefits

2. Purpose of this System Design
This document provides valuable insights into system architectural planning and a
comprehensive compliance strategy for our KYC, GDPR, and digital identity laws in our
MVP countries. It outlines a common technical blueprint to guide our Technical
implementations.
More details elaborated here is the end-to-end architecture and roles of each subsystem and
microservice, also to show the functional use cases of our key components, and more
importantly, the data flows. Security is key to the project, and compliance controls have to be
implemented to mitigate risks, also to make sure to follow the requirements of countries such
as Ethiopia and Rwanda.
Also, a plan to make the system functional and available at all times to handle loads and peak
times, and scale as needed, with a working operational structure.
3. Proposed System Architecture.
[AeTrust Input] Provide high-level overview and the core of the platform including the transaction engine:
•	High-Level Overview: The system is composed of.....
•	Transaction Engine: This is the core of the platform. simple description
o	Real-time Processing: explain
o	Atomicity: explain how
o	Validation: The engine performs a series of real-time validations, including:
	Transaction Limit Enforcement (NBE Requirement): explain
	Balance Check: explain
	Tired Level KYC (NBE Requirement): explain
	Fraud Detection: explain
•	Security Features: simple description
o	List out and explain the security features

•	Authentication and Authorization: simple description
o	Two-Factor Authentication (2FA) (NBE Requirement): explain
o	Role-Based Access Control (RBAC): explain

•	Core Architecture Features: simple description
o	List out and explain the core architecture features

•	Components and their Roles: simple description
o	Persistent Components: simple description
	List out and explainthe persistant components found in the system
o	Backend Components: simple description
	List out and explain the back end components
o	Frontend Components: simple description
	List out and explain the frontend components

The Entire Microservice codebase will be written in Java because it’s enterprise-grade,
highly scalable, has mature frameworks like Spring Boot, and is widely adopted in fintech for
building secure, reliable, and maintainable microservices.
Note: Because fintech platforms process high-value transactions and personally identifiable
information (PII), we will be providing a defense-in-depth security posture adopting a
Zero-Trust Architecture (ZTA), enforcing end-to-end encryption with TLS 1.3 in transit and
AES-256 at rest, and implementing automated cryptographic key rotation via
enterprise-grade KMS/Vault to ensure compliance with PCI-DSS, SOC 2, and GDPR while
mitigating risks of data exfiltration, man-in-the-middle attacks, and insider threats.

One of the keys things is that we must build compliance-by-design compatible framework
which help ensures regulatory adherence by embedding data minimization principles,
maintaining immutable audit trails for traceability, enforcing granular consent management,
applying jurisdiction-aware PII tagging (e.g., jurisdiction: ET), and enabling selective data
localization to align with cross-border data residency laws, GDPR, CCPA, and regional
financial regulations all of which reduce compliance risk while ensuring audit readiness
which helps us to avoid governmental backlash.
It has to be Modular and independent with versioned APIs, ensuring a lifecycle autonomy,
also to be resilient with the use of Circuit breakers, Idempotent retry policies, exponential
backoff with jitter, which guarantees fault tolerance and high availability
3.1 Physical / Infrastructure Components
● Kubernetes: To orchestrate stateless microservices, providing automated scaling,
self-healing, and streamlined deployments.
● PostgreSQL: This is our primary transactional database, ensuring consistency, high
availability, and reliability for core business data.
● Apache Kafka: Powers our event-driven architecture, enabling real-time data
streaming, messaging, and integration with Change Data Capture (CDC) pipelines.
● Redis (Clustered): Provides ultra-low-latency caching, rate-limiting, distributed
locks, and support for ephemeral user sessions.
● Object Storage (S3-compatible) or Cloudinary: Handles our persistent storage of
documents such as KYC files, system snapshots, and long-term event archives.
● CI/CD (GitLab CI): Automates our build, test, and deployment pipelines, with
integrated security checks like container image scanning and dependency
vulnerability analysis.
3.2 Logical Architecture (Layered View)

[AeTrust Input] Add 3.2.1
3.2.1 Platform – Sample Roles and Hierarchies for System Users
This section defines the access control based on user roles, as part of the overall security framework. 
•	You should mention hierarchies that are found within the system and explain their roles

3.2.1. Channel Layer
This is responsible for all customer-facing touchpoints. Key Components for Aetrust are the
USSD gateway, Mobile Apps (iOS/Android), Web frontend, and Merchant/Agent
infrastructure. It helps in device and channel-specific presentation, input validation, local
session handling (short-lived), and minimal business logic (routing & format conversion).
We are keeping this layer thin and stateless, which pushes heavy logic to the core services.
3.2.2. Edge & Access Layer
Acts as the secure entry point to the platform and enforces access policies. Key components
are API Gateway, Web Application Firewall (WAF), Bot protection, Rate limiter, which helps
in TLS termination, request validation, API routing, authentication & authorization, DDoS
and bot mitigation, global throttling and quotas, canarying/capacity control for new API
versions.
3.2.3 Core Services Layer
These are our independent microservices in Java. Also, each service is a bounded context
with its own data model and API. Key Services include :
● Identity & KYC Service: We require National ID / MSISDN verification, consent
management, DPO workflows, jurisdiction tagging, and secure document
orchestration (object store pointers only).
● Auth & IAM Service: The use of JWT issuance/validation, refresh token lifecycle,
RBAC for internal users, session security, and admin access controls.
● Wallet & Ledger Service (Core): System of record for our accounts and balances,
double-entry ledger model, idempotency, due to the strong ACID transactions in
Postgres.
● Payments & Remittance Engine: Our PSP connectors perform routing,
retry/compensation flows, FX conversion, settlement orchestration, and
reconciliation.
● Agent Service: Inbuilt Cash-in/cash-out flows, commissions, agent hierarchies, and
reconciliation for agents.
● Lending Service: The system performs Loan approvals, disbursement, repayment
scheduling, and collections orchestration.
● Savings & Investment Service: Built-in Vaults, group savings, interest accrual, and
payout.
● Notifications Service: Proposed is a Kafka-driven consumer for Amazon SQS or
Sendgrid
● Compliance & AML Service: Our Real-time streaming rules engine, alert generation,
case management integration, and sanctions screening with respect to Rwanda and
Ethiopia.
● Admin & Compliance Portal: We have Portals for operations, reporting, dispute
resolution, and audit access with PII redaction.
● Observability & Audit Service: We have a Centralized collection of audit events,
immutable logs, trace correlation, and SIEM forwarding.
● Bill Payment.

[AeTrust Input] Add new chapters: Chapter 4, 5, and 6
4. System Requirements
This section details the physical and logical infrastructure.
•	Add the system requirements for the service, we have provided you with some that is required by NBE below. Explain and describe each 
•	DR Configuration (NBE Requirement): 
•	Data Center Redundancy (NBE Requirement): 
5. IT Network and Security Arrangement
•	List out and discribe the IT network and security arrangement we have mentioned 1 that is NBE reqirement
•	Network Redundancy (NBE Requirement):
o	Dual ISP Links: explain
o	Redundant Switches and Routers: explain
6. Compliance-Specific Technical Requirements
•	List out and discribe the IT network and security arrangement we have mentioned 6 that is NBE reqirement. You should add more
•	Secure Data Storage and Backup (NBE Requirement):
o	Data Integrity: explain
o	Regular Backups: explain
•	2FA for Transactions exceeding Birr 5,000 (NBE Requirement): explain
•	Interoperability via National Standard (NBE Requirement): explain
o	QR Code Standard: explain
o	EthSwitch Integration: explain
•	Real-time Processing (NBE Requirement): explain
•	Tiered Level KYC (NBE Requirement): explain
•	Transaction Limits Enforcement (NBE Requirement): explain
•	APIs and Integration: explain
o	Open Banking APIs: explain
o	API Documentation: explain

[AeTrust Input] Provide clear full resolution diagram files or images. Blow are dark and not clear
3.2.1 Logical Architecture (Diagram View)
 
dig 1.0

3.3 Database Model
 
dig 2.0

Technical Implementation Roadmap
Vision
We look forward to delivering a secure, scalable, and compliant digital financial services platform that empowers merchants, agents, and individuals in Rwanda and Ethiopia to access seamless payments, remittances, lending, and savings solutions.
Mission
To implement the AeTrust platform through a phased, agile, and compliance-driven development approach, ensuring that all functional and regulatory requirements are met, tested, and deployed within the agreed timeline (September 1 – November 1, 2025).
Purpose of the Roadmap
The roadmap stands as a guiding framework for the successful execution of the project. It explains the phases, timelines, deliverables, and responsibilities, ensuring all stakeholders (technical, compliance, and business) are aligned. It also provides visibility into milestones, ensuring that development, testing, compliance, and deployment are completed on time.
PLEASE NOTE
Each phase is planned in collaboration with the developers to ensure a timely and accurate result. All modules of the Project will be worked on at the same time; some of the modules are connected, which cannot permit working on separate modules at a time. Each phase spans 3 weeks, 3weeks, 1 week, and 10days respectively
Phases of Implementation
Phase 1 – Development of the Modules (Sept 1 – Sept 22, 2025)
Objectives: Ensure setup, development, and testing
 Project Setup and  Onboarding page
Onboarding page
Home and Card 
Top Up     
 Bill Payment
Notification
Testing: All Units in this phase will be available for testing
Deliverable: All listed pages on the 3 modules are done and tested
Owner: Mobile Engineer, Web Developer, and Backend Developer

Phase 2 – Core Development of the Aestrust User, Agent, and Merchant module (Sept 23 – Oct 14, 2025)
Objectives: Ensure development and testing for all functionalities, API Integrations, and Security Compliance
Transactions of the 3 modules
Transfer wallet-to-wallet P2p
Transfer International
Wallet-to-wallet P2p
B. Integration with the MTN Momo API ( Sep 21, 2025) and the Onafriq API for payment in Rwanda and Ethiopia ( Sep 28, 2025)
Confirm the current implementation of the Aetrust Gateway for connection. Otherwise:
Obtain API Documentation from MTN MoMo (Rwanda & Ethiopia) and Onafriq
Develop authentication flow
Functional, Integration, Performance, Compliance, and User Acceptance Testing
Testing: All Units in this phase will be available for testing
Deliverable: At the end of this phase, all Transaction pages for the 3 modules are ready for testing


Phase 3 – Extended Features (Oct 15 – Oct 22, 2025)
Objectives: Build value-added services and integrations.
Loan and Profile for the 3 modules
Admin dashboard
USSD
Testing: All Units in this phase will be available for testing
Deliverable:  All loan and profile pages are available for testing
Owner: Mobile Engineer, Web Developer, and Backend Developer

Phase 4 – Testing, Deployment & Go-Live (Oct 23 – Nov1, 2025)
Objectives: Ensure quality, performance, and regulatory compliance.
 Testing each module and page
Ensuring all Qualities are met
Security and Compliance are checked to meet the requirements
Deliverable: All Pages are Fully Tested and Deployed
Owner: Mobile Engineer, Web Developer, Backend Developer, and Project Manager.
Timeline Overview
Phase 1: Sept 1 – Sept 22 (3 weeks)
Phase 2: Sept 23 – Oct 14 (3 weeks)
Phase 3: Oct 15 – Oct 22 (1 week)
Phase 4: Oct 23 – Nov 1 (9 days)


Total Duration: 8weeks

Structure for Testing
Starting from the 1st of October, the following functionalities will be ready for a unit test:
Onboarding page for the User, Agent, and Merchant Module
Onboarding page for the User, Agent, and Merchant Module
Home and Card for the User, Agent, and Merchant Module
Top Up  for the User, Agent, and Merchant Module 
 Bill Payment for the User, Agent, and Merchant Module
Notification for the User, Agent, and Merchant Module


1.0 Compliance Strategy
1.1 Ethiopia & Rwanda Market Entry Framework
This is our strategic approach to regulatory compliance and market entry for Ethiopia and Rwanda. Our strategy focuses on proactive engagement with regulators, phased market entry, and building trust through transparency while maintaining operational efficiency.
To understand deeply, let's understand: 
Ethiopia Regulatory Compliance Framework
Primary Regulator: National Bank of Ethiopia (NBE)
Compliance Requirements
1.2 Data Localization (Mandatory)
ALL customer data must be stored within Ethiopian borders.
No cross-border data transfers without explicit NBE approval.
Local data centers or cloud regions are required.
Real-time data sovereignty monitoring is needed.
Breach notification within 48 hours.
   Implementation Strategy deduced from our research, we need to :
Deploy dedicated Ethiopian cloud infrastructure (AWS Africa Cape Town + local) to make sure data stays within or preferably goes totally local.
Implementation of geo-fencing at the application level also includes data localization controls
Need to create data residency validation checks
Need to establish local backup and disaster recovery
Establish relationships with local banking partners
Continuous compliance monitoring
An automated FX compliance monitoring is needed for the FX use case
Real-time transaction flagging system
Integration with NBE reporting systems
Legal entity establishment for FX operations

1.3 Payment System Integration
Leveraging on Mtn Momo and the Onafiq Framework is feasible and within regulations

1.4 Foreign Exchange Controls
Exchange Rate Determination: Exchange rates can be determined by banks and authorized dealers through open market negotiations, with the NBE publishing an Indicative Daily Exchange Rate.
Foreign Currency Accounts: Regulations for foreign currency accounts have been eased for various entities, including foreign institutions, FDI companies, international organizations, and the Ethiopian diaspora.
Exporters' Foreign Currency Retention: Exporters must convert 50% of their foreign exchange earnings to Birr and can retain the remaining 50% indefinitely. Companies in Special Economic Zones can retain 100% of their earnings.
Penalties for Violations: Violations can lead to criminal liability, imprisonment, and fines. Banks and licensed entities face sanctions and potential license revocation.
1.5 Ethiopian Partnership Strategy
 Which is highly recommended for future growth, borrowing ideas from Opay Limited approach:
Establishment of relationships with local banking partners, which helps to have more base-level integration with the users.
Regulatory credibility and deepened local knowledge
Existing customer base and distribution channels
Technical infrastructure sharing
Risk mitigation through local expertise

1.6 Rwanda Regulatory Compliance Framework
1.6.1 Critical Compliance Requirements
A) GDPR-ALIGNED DATA PROTECTION
   It's more flexible than Ethiopia because there is Consent-based data processing
   Right to access, rectification, and erasure
   Data Protection Impact Assessments (DPIA)
   Appointment of Data Protection Officer (DPO)
   Breach notification within 48 hours
 



  
Implementation Strategy:
Need to implement a comprehensive consent management system
Automated data subject rights fulfillment
Regular DPIA assessments for new features
Dedicated DPO appointment and training
Incident response procedures for data breaches
B) PAYMENT SYSTEM INTEGRATION:
Leveraging on Mtn Momo and the Onafiq Framework is feasible and within regulations
  Integration with the Rwanda Integrated Payment Processing System (RIPPS)
   Real-time transaction monitoring and reporting
  Compliance with payment system standards
1.6.2 Technical Market Entry Strategy
Deployment of cloud infrastructure AWS, preferably in South Africa's Server
Implementation of  GDPR compliance controls
Continuous compliance monitoring
System optimization based on feedback
1.7 Rwanda Partnership Strategy Same as Ethiopia
Partnership Benefits:
Establishment of relationships with local banking partners, which helps to have more base-level integration with the users.
Regulatory credibility and local knowledge
Existing customer base and distribution channels
Technical infrastructure sharing
Risk mitigation through local expertise

1.8 Remittance Corridor Compliance
1.8.1 Ethiopia-Rwanda Corridor Challenges
The difference in regulatory frameworks and requirements is a huge factor that needs to be looked into
Currency conversion and FX controls
Cross-border data transfer restrictions
AML/CFT compliance across jurisdictions
1.8.2 Implementation Strategy
Need for separate legal entities in each country
Implement jurisdiction-aware transaction routing
Development of tailored country-specific compliance engines
Creation of cross-border monitoring and reporting systems

2.0  Data Governance Framework
2.1 Data Classification:
Ethiopian Customer Data - Must remain in Ethiopia
Rwandan Customer Data - Can be processed in GDPR-compliant regions
Transaction Data - Subject to both jurisdictions' requirements
Operational Data - Flexible based on business needs
2.1.1 Technical Implementation being implemented:
Geo-tagging of data storage and processing
Automated data residency enforcement
Cross-border data transfer controls
Jurisdiction-specific encryption keys

2.2 Regulatory Risk Assessment
2.2.1 High-Risk Areas
Data localization violations (Ethiopia)
GDPR compliance failures (Rwanda)
AML/CFT control weaknesses
Foreign exchange violations
Licensing and operational compliance




3.0  Critical Compliance Requirements from NBE Directive ONPS/01/2020
Our system must implement the following key rules from the directive:
 Licensing: We operate as a licensed Payment Instrument Issuer (PII), not a bank. This dictates our capital requirements, ownership structure, and permitted activities.
 Electronic Money Float (Art. 10): 100% of user funds must be held in a segregated, blocked account at a licensed bank in Ethiopia (and/or in NBE-approved government securities). This fund must be reconciled daily against the sum of all user electronic money account balances.

Account Tiers & Limits (Art. 8) The system must enforce three distinct account levels with strict balance and transaction limits:
Level 1: Max balance: ETB 5,000 | Daily Tx Limit: ETB 1,000 | Monthly Tx Limit: ETB 10,000
Level 2: Max balance: ETB 20,000 | Daily Tx Limit: ETB 5,000 | Monthly Tx Limit: ETB 40,000
Level 3: Max balance: ETB 30,000 | Daily Tx Limit: ETB 8,000 | Monthly Tx Limit: ETB 60,000

 Customer Due Diligence (KYC) (Art. 11): Specific KYC information are being captured and stored for each account level. Level 1 requires an introduction from an existing user.
 Dormant Account Handling (Art. 9): Automated processes must classify accounts as dormant after 12 months of inactivity, attempt to notify the user, and eventually close the account, transferring funds to a designated bank account while maintaining records for 10 years.
 Agent Management (Various): Clear distinction between user, merchant, and agent accounts. Agent accounts have no transaction limits for user-related transactions.
 Data Localization: all user data, transaction records, and the float account must reside within Ethiopia.




 Reporting (Art. 13):
 Mandatory quarterly reporting to the NBE on a comprehensive set of metrics, including accounts, transactions, volumes, values, fraud incidents, and system performance.

The system will be ready to generate reports proving capital allocation upon audit.

Currency (7.1)	All accounts and transactions must be denominated only in Ethiopian Birr (ETB).	
Enforce currency code ETB as a hard-coded constant in the account_table and validate on all transaction requests in the Wallet Service. Reject any transaction requesting a different currency.

Real-Time Processing (7.3)	
All transactions must be electronic and in real-time. Wallet & Ledger Service for low-latency processing. Use asynchronous, non-blocking I/O where possible, but we ensure transaction finality is immediate from the user's perspective.

Authentication (7.4)
	2-Factor Authentication (2FA) mandatory for transactions > ETB 1,000. Implementation of 2FA (SMS OTP or App-based TOTP) in the Auth Service. The API Gateway and Wallet Service will enforce this check based on transaction amount.

Account Tiers & Limits (8.1)	
Three distinct account levels (L1, L2, L3) with strict limits on balance and transaction volume.	This is CORE BUSINESS LOGIC. The user_table will have a tier_level field. The Wallet Service will enforce these limits on every transaction. We will use Redis to track daily and monthly aggregate sums per user.

Agent Accounts (8.6)	
Agent accounts have no limits for user-facing transactions. Implementation  of an account_type field (USER, AGENT, MERCHANT, SYSTEM_FLOAT). Logic in the Wallet Service will bypass limit checks for transactions where the sender or receiver is an AGENT-type account.

Float Reconciliation (10.5)	
DAILY reconciliation of total e-money value vs. bank float account balance. Report to NBE by 10:00 AM the next day. This is the MOST CRITICAL compliance task. We will build a dedicated Float Management Service with a scheduled job that:
1. Queries the DB for total user balances.
2. (If possible) calls bank API for balance; else, manual upload.
3. Compares the two.
4. Generates an automatic report and alerts via PagerDuty/Slack on any discrepancy.
5. Must be rectified by 12:00 PM.




Fund Segregation (10.12)	
User funds must be segregated from operational funds.	Implement a dedicated SYSTEM_FLOAT account in our double-entry ledger. User transactions always interact with this account. Our own operational accounts are separate.

Data Localization	
Implied requirement for PII and financial data.	All infrastructure (Kubernetes, PostgreSQL, S3) MUST be provisioned within Ethiopia. No exceptions. This is a blocker for go-live.

Record Keeping (15.3)	
All transaction records must be kept for 10 years.	Our PostgreSQL database hosting the transaction_table must be designed for long-term storage and easy audit retrieval. A data archiving policy must be established.

Quarterly Reporting (13.2)	
Extensive reporting required on accounts, transactions, agents, fraud, etc.	We will build automated jobs within a Compliance Service to generate these reports (CSV/Excel) at the end of each quarter for easy submission to the NBE. This cannot be a manual process.

3.1 Enhanced System Architecture for Compliance
The architecture remains microservices-based but with compliance services deeply integrated into the core flow.
3.2 Key Technical & Compliance Implementation Details
1. Data Encryption & Tokenization (PCI-DSS & NBE Compliance)
Implementation: A dedicated Tokenization Service microservice will be created.
Process: Upon user registration or card linking, sensitive data (e.g., national ID number, bank account number, any future card PANs) is sent directly to the Tokenization Service via a secure, internal API call.
It encrypts the data using AES-256 and stores the ciphertext in a highly secure, isolated vault database.
It returns a unique, random token (e.g., tok_78fFg92nMd) to the calling service (e.g., Identity Service).
The calling service stores only this token in its PostgreSQL database instead of the actual sensitive data.
Retrieval: When the plaintext value is needed (e.g., for bank settlement reconciliation), a service with appropriate permissions calls the Tokenization Service with the token to decrypt it. This access is heavily logged and audited.
Benefit: The actual sensitive data is stored in very few, highly secured locations. The rest of the system uses tokens, drastically reducing the risk of data exposure in case of a breach.

3.3.  Wallet & Ledger Service
Implements a double-entry accounting system in PostgreSQL, ensuring accurate, auditable balance tracking.


Each account is linked to KYC identifiers (National ID, MSISDN, biometric/consent records) stored securely in the Identity & KYC Service.


Jurisdiction-aware PII tagging ensures Ethiopian accounts are localized while Rwandan accounts adhere to GDPR controls.


Accounts support tiered levels (basic, agent, merchant) with configurable limits.


Account Limits & Float Management: 
Database Schema: The wallets table will have columns for account_tier (ENUM: 'LEVEL_1', 'LEVEL_2', 'LEVEL_3', 'AGENT', 'MERCHANT'), max_balance, daily_tx_limit, monthly_tx_limit_remaining, and last_reset_date.
3.3.1. Logic
 Before any transaction (debit or credit), the service will check:
Would this transaction cause the balance to exceed the max_balance for their tier?
Has the user exceeded their daily transaction limit or monthly transaction limit?
This is a hard, non-bypassable check within the transactional boundary.


3.3.2. Float Reconciliation
 A daily cron job (scheduler) will:
Query the sum of all user wallet balances from the Ledger Service.
Query the balance of the designated Electronic Float Account from the bank via an API.
Compare the two values. Any discrepancy must be investigated and resolved by 12:00 PM the next day as per NBE rules. A reconciliation report will be automatically generated and sent to the NBE portal/service.
3.4.  Identity & KYC Service (Customer Due Diligence):
The service will enforce different KYC workflows based on the requested account tier.
Storage: KYC documents (scanned IDs and photos) will be stored in an encrypted Object Storage bucket. Only a pointer (file path) and metadata will be stored in the database. The database will store the KYC status (PENDING, APPROVED, REJECTED) and the achieved account tier.
3.5.  Compliance & Reporting Service:
This service will consume events from Kafka (e.g., TransactionCreated, UserRegistered, AccountDormant).
It will maintain aggregates and data models specifically for the quarterly NBE reports.
It will expose an API for the Admin Portal to generate and download these reports in the required format for submission.
It will also handle automated dormancy notifications and processes.



4.0. Rwanda FinTech Compliance & Technical Requirements

4.1.1 Licensing
A Rwanda-based fintech must be licensed by the National Bank of Rwanda (BNR) as a Payment Service Provider/e-money issuer. Non-bank issuers must follow the BNR’s licensing rules (similar to Rwanda’s Regulation No. 54/2022), including meeting capital and governance requirements. Deposit-taking banks can issue e-money only with BNR authorization.


4.1.2. E-Money Float & Safekeeping
 By law, 100% of outstanding e-money (the “float”) must be held in trust (segregated) accounts at licensed banks or in approved short-term government securities. We must reconcile the total trust balance daily against all user balances – for example, by 4:00 p.m. each day – and rectify any shortfall by noon the next day

 The trust fund must never fall below what is owed to customers and cannot be encumbered or used as collateral

 In practice, the system maintains a “SYSTEM_FLOAT” ledger account that reflects the on-bank trust balance; all user wallets are contra-entries to this account. Diversification rules mean no single bank may hold more than 20–25% of the float, so excess must be split into multiple banks

 Weekly reports (per BNR format) of the trust reconciliation must be submitted to BNR, and annual audited financials (including proof of trust-fund allocation) must be available on demand.



4.1.3. Account Tiers & Limits:
 Rwanda regulation mandates tiered e-money accounts (Tier I–IV) by customer type

 By rule, Tier I includes individual users, Tier II includes legal entities, Tier III includes agent outlets, and Tier IV includes merchants. The system will enforce these tiers (e.g., via a tier field on user accounts). Unlike Ethiopia’s fixed limits, Rwanda uses a risk-based approach: e-money issuers set appropriate balance/transaction limits per tier that are “commensurate with the purpose” of the account

 For security, any wallet limit above RWF 50,000,000 requires prior BNR approval.
 In practice, we implement configurable per-tier limits in the Wallet Service and ensure no user or account exceeds these thresholds.




4.1.4. Customer Due Diligence (KYC)
The system must collect KYC details appropriate to each tier. Per BNR rules, customer identity must be verified via Rwanda’s National Identification Agency (NIDA) or an equivalent authoritative source

Each e-money holder receives a unique customer ID, and no “anonymous” or fictitious accounts are permitted

 A single customer may hold up to three e-money accounts (all linked), unless the issuer obtains special permission from BNR
 The KYC process must also include AML/CFT checks: new customers’ identities are screened against criminal/terrorist lists, and under-age (<16) applicants require a verified guardian. All KYC data (IDs, documents) is stored securely (e.g., encrypted in a vault or tokenized) and linked to user records. In summary, the system’s Identity/KYC Service will enforce the tier-based KYC workflow mandated by Reg 54/2022 (tiers, ID verification, unique IDs)

.

4.1.5. Dormant & Inactive Accounts
We need to implement automated dormancy rules consistent with BNR guidelines (Reg 55/2022). In Rwanda, accounts unused for 6 months (and zero balance) are considered dormant (no fees allowed), and those inactive for 12 months are deemed closed

 In practice, the system will flag inactivity (e.g., no login or transaction) after 6–12 months, notify the user via SMS/USSD, and if still unused by 12 months, automatically close the account. Remaining customer funds are moved to a designated trust or suspense account in compliance with BNR procedures. All dormant/closed account records (and any fund transfers) are logged for audit; per regulation, no maintenance or reactivation fees may be charged

.

4.2. Agent & Merchant Management
 The system distinguishes User, Agent, and Merchant accounts. Agents (Tier III) are retail outlets authorized to process customer cash-ins/outs. Per BNR rules, agent accounts are special: they are not subject to personal wallet or transaction limits when performing user-cash-in/cash-out functions. (We will implement an account_type field and skip limit checks when “from” or “to” is an Agent.) All agents must be onboarded via the Agent KYC process, and agent transaction volumes are reported separately. Merchant accounts (Tier IV) act like businesses (receiving payments for goods/services) and may have higher risk/scoring in AML monitoring. (Note: no interest may be paid on any e-money; merchants/agents earn commissions per agreement.)


4.3. Data Localization & Security
Rwanda’s Data Protection Law (2021) mandates strict protection of personal data. We will host all customer PII, transaction records, and float-related data on servers/databases within Rwanda. Cross-border transfers of Rwandan personal data are allowed only with explicit safeguards (e.g., customer consent, contractual necessity, or BNR/NCSA approval)
securiti.ai
 In practice, all infrastructure (Kubernetes, databases, storage) will reside in-country by default. Sensitive PII (ID numbers, biometrics) will be encrypted and tokenized via a dedicated Tokenization Service (AES-256 encryption) so that only tokens are stored in the core databases. Any call to retrieve decrypted data (e.g. for bank settlement) will go through the Tokenization Service with full audit logging. This minimizes risk of breaches in alignment with best practices (and PCI-DSS if card data is ever held). The system will also enforce the Rwanda data principles: data minimization, purpose limitation, and retention only as long as needed.

4.4 Transactions & Currency
 All e-wallets and transactions are denominated in Rwandan Francs (RWF) only. The account data model will hardcode the currency as RWF and reject any request in other currencies (per NPS Law guidelines). Transactions must be processed in real-time (online) – no store-and-forward batch processing – to ensure finality and meet BNR’s requirement that payments be instantaneous. The Wallet & Ledger Service will be designed for low latency (asynchronous I/O, idempotent APIs) so that customers see immediate balance updates.

4.5. Authentication & Fraud Controls 
For security, we will implement 2-Factor Authentication (2FA) for high-value operations. For example, any outgoing transaction above a threshold (e.g., RWF 100,000) will require a one-time password (sent via SMS or generated by an authenticator app). The Auth Service (part of the API Gateway) will enforce this rule on each transaction request. All customer sessions will use strong session tokens (JWTs with short TTL) and role-based access control. Additionally, transaction-level fraud scoring rules (e.g., velocity checks, PEP list screening, geolocation anomalies) will run in real-time via the Compliance Service (consuming Kafka events). Any suspicious activity will generate alerts for investigation.

4.6. Reporting & Audits
 The system will automate all required regulatory reports. In addition to the trust-fund weekly reports, the Compliance Service will produce quarterly and annual reports on: total accounts by tier, transaction counts/values, agent/merchant networks, fraud incidents, and system performance metrics. These reports (CSV/Excel) will conform to BNR’s templates. We will also log all actions for audit; for example, every fund reconciliation, user notification, and admin change is time-stamped. The system will be ready to provide auditors with proof of capital allocation and trust-account allocations at any time.




5.0. System Architecture for Compliance

We will build a microservices architecture with specialized services enforcing these rules:

5.1.1. Tokenization Service
 A secured microservice for encrypting/decrypting sensitive data. All PII (national ID, passport numbers, bank account numbers, future card PANs, etc.) will be sent here via API and stored as opaque tokens (e.g., tok_xxx) in our databases. Decryption requests (e.g. for settlement) require special privileges and are fully logged. This isolates raw PII in a hardened vault.

5.1.2. Identity & KYC Service
 Manages customer profiles and onboarding. It interfaces with Rwanda’s National ID database (NIDA) for e-KYC (verifying ID numbers). It also stores KYC documents (encrypted in object storage) and tracks account tier eligibility. The service enforces the Reg 54/2022 rules: it will not open an account unless ID is verified and AML checks passed. For under-16 users, it links the wallet to a guardian. Each user’s tier is set here after review.

5.1.3. Wallet & Ledger Service
 Core double-entry accounting for all customer balances. Each customer/agent/merchant has a wallet account; every operation generates ledger entries. This service enforces tier-based limits: before a debit or credit, it checks the customer’s tier and ensures the new balance and daily/monthly totals are within allowed limits (as configured). Agents bypass these checks on behalf of customers. The ledger always keeps the aggregated float in sync with the trust account (SYSTEM_FLOAT). A daily cron (the Float Reconciliation job) will query the total of all wallets and compare it to the bank float balance; discrepancies trigger PagerDuty alerts and a report.

5.1.4. Transaction/Compliance Service
 All transactions pass through this layer. It publishes events (TransactionCreated, etc.) to Kafka. Downstream, the Compliance Service (a Kafka consumer) applies AML/CFT rule engines (detect structuring, fraud patterns, sanctions hits) in real time. It also tracks quotas for daily/monthly limits (e.g. via Redis counters) and resets them at period end. Any blocked/flagged transaction is reported.

5.1.5. Auth & API Gateway
 An API Gateway enforces authentication and routing. The Auth Service issues JWT tokens after login (with credentials and SMS OTP for first login). It will implement 2FA logic: e.g., on a high-value transfer, the API first demands an OTP challenge. The gateway also validates roles (user vs agent vs merchant) on each call.





5.1.6. Compliance & Reporting Service
In addition to real-time monitoring, a background component will generate the scheduled statutory reports (quarterly NBR reports, KYC metrics, agent performance). It will also trigger dormancy workflows: e.g., daily, it scans accounts with 5 months of inactivity and queues reminder messages; at 12 months, it executes closure procedures.

All infrastructure (Kubernetes clusters, PostgreSQL databases, object storage) will be provisioned in Rwanda to comply with data residency. Communication between services uses mTLS. Logs and metrics are aggregated in a SIEM for security monitoring.

5.1.7. KYC / AML-CFT

We will enforce tiered KYC: individuals vs corporates vs merchants, each have incremental requirements. Electronic verification links to Rwanda’s National ID registry
Sensitive personal data is protected under law (with DPO oversight). All customers undergo AML screening: new accounts are checked against sanctions/PEP lists (as per Reg 54/2022, Art. 9)
Real-time AML/CFT monitoring uses a rule engine on the transaction stream: e.g. large cash-ins/outs or rapid transfers trigger alerts. The system can adjust transaction caps if suspicious activity is detected. All suspicious cases are escalated to BNR’s Financial Intelligence Unit (KICU) via secure channel, as mandated by Rwandan AML law. We also file the required periodic AML reports to regulators. KYC data is updated periodically, and stale accounts are reviewed for re-verification.


5.2. Dispute Resolution & Case Management

A built-in case management module (accessible via the Admin portal) will log all customer disputes, complaints, or chargebacks. Each case is tracked with status and resolution deadlines. The workflow enforces escalation: unresolved issues may be sent to higher support tiers or regulators (e.g., Rwanda Utilities Regulatory Authority if telecom issues, or RURA if broadcasting issues). All communications and outcomes are archived for audit. We will adhere to any Rwanda-specific consumer redress rules (e.g. if BNR issues guidelines on complaint handling). Customer Protection Policy

AeTrust Rwanda will adopt a robust customer protection framework: we treat all customers fairly and without discrimination. All fees (account opening, closing, maintenance) comply with BNR’s consumer rules

  for example, no fees at account opening or closing, and no fees on dormant accounts with zero balance. Fees and exchange rates are transparently disclosed up front.

We maintain 24/7 customer support (hotline, USSD code, WhatsApp/WhatsApp Business) with multilingual staff. A clear terms-of-service agreement (in Kinyarwanda/English) and privacy policy will be presented at onboarding. Customer PII is kept strictly confidential (under Rwanda’s Data Protection Law), and no account action is taken without user consent or legal requirement. In short, our UX and policies will reflect the high standards of consumer protection expected by Rwandan regulators and customers.

5.3 Similarities (Ethiopia vs Rwanda)
Both countries converge on the core regulatory principles for fintechs / e-money issuers:
Licensing


Both require non-bank fintechs to be licensed as Payment Instrument Issuer (Ethiopia) or Payment Service Provider (Rwanda) under central bank supervision.


Both impose capital requirements, governance conditions, and regulator vetting.


Float / Safeguarding of Funds


100% of customer balances must be held in a segregated trust/float account with a licensed local bank.


Daily reconciliation of float vs system balances is mandatory.


Shortfalls must be rectified by the next business day.


Account Tiers & Limits


Tiered wallet/account levels tied to KYC depth.


Higher levels = higher transaction and balance limits.


Agent accounts are exempt from user-facing limits (since they handle liquidity for customers).



KYC / AML


Tier-based KYC (basic vs advanced).


Verification against national ID systems (NID Ethiopia, NIDA Rwanda).


AML/CFT checks (sanctions/PEP lists, suspicious activity monitoring).


Dormant Accounts


Dormancy rules: inactive accounts are flagged and eventually closed.


Dormant accounts must be reported and funds transferred appropriately.


No reactivation/maintenance fees allowed.


Currency
Only local currency allowed:
Ethiopia = ETB.


Rwanda = RWF.
Cross-border/multi-currency requires explicit approval.


Reporting


Both require quarterly/annual reports on accounts, transactions, fraud, and system health.


Both require float reconciliation reports.


Data Localization


Both require customer/transaction data to be stored inside the country.


Cloud infrastructure must be in-country or regulator-approved.





Security


Both mandate real-time processing of transactions.


2FA for high-value transactions.


Encryption of sensitive PII and strong authentication.


Record Keeping


Long-term data retention: Ethiopia = 10 years; Rwanda = aligned with DP law (often 10 years for financial records).
5.4. Ethiopia vs Rwanda – Compliance & Regulatory Differences
Account Tiers


Ethiopia (NBE ONPS/01/2020): Fixed, explicit numeric limits (L1: 5k ETB, L2: 20k, L3: 30k).


Rwanda (BNR Reg. 54/2022): Tier-based by customer type (I–IV: individual, entity, agent, merchant). Limits are risk-based, set by the issuer, but require BNR approval if high (≥ RWF 50M).


Dormancy


Ethiopia: Dormant = 12 months of inactivity; must close and transfer funds to the designated bank.


Rwanda: Dormant = 6 months inactivity (zero balance); closed at 12 months. No fees allowed on dormant accounts.


Float Diversification


Ethiopia: Silent on diversification (single bank float account allowed).


Rwanda: Explicit diversification rule – no >20–25% of float with one bank; must spread across multiple banks/securities.


Currency Enforcement


Ethiopia: Hard-coded ETB only.


Rwanda: Hard-coded RWF only, but cross-border rules explicitly allow approval-based foreign settlement.


KYC Details


Ethiopia: Level 1 requires introduction by an existing user.


Rwanda: No introductions; relies on NIDA verification + AML screening.


AML Monitoring


Ethiopia: Suspicious transactions flagged and reported to NBE FIU.


Rwanda: Same, but mandates reporting to KICU + stricter linkage (max 3 wallets per person).


Data Protection


Ethiopia: No comprehensive DP law yet, but data localization is mandatory.


Rwanda: Full Data Protection Law (2021) – requires DPO, lawful basis for processing, explicit consent, and rights of access/erasure.


Reporting Frequency


Ethiopia: Quarterly reporting to NBE.


Rwanda: Weekly float reconciliation reports + quarterly/annual activity reports.


Minors


Ethiopia: Not explicitly mentioned in the directive.


Rwanda: Explicit – under 16 must link account to guardian.


Interest on E-Money


Ethiopia: Silent.


Rwanda: Explicitly forbids paying interest on e-money balances.



5.4. Things to Note for Your Platform Design
System Parametrization


Ethiopia has fixed numeric thresholds → hardcoded values per directive.


Rwanda has configurable risk-based thresholds → must be adjustable in the system (via admin portal).


Float Management


Must support multi-bank float accounts in Rwanda.


Ethiopia = single float account suffices.


KYC Workflow


Ethiopia: Level 1 needs peer introduction.


Rwanda: Must integrate with NIDA API for ID verification.


Design modular KYC service to handle different country rules.


Data Handling


Ethiopia: Strict localization only.


Rwanda: Localization + GDPR-like DP law → need consent management, DPO role, audit trails.


Dormancy Automation


Ethiopia = 12-month closure.


Rwanda = 6-month dormancy, 12-month closure.


Must build a jurisdiction-aware dormancy job.




AML/Transaction Monitoring


Both require real-time AML engines, but reporting destinations differ (NBE FIU vs KICU).


Use configurable reporting connectors.


Currency


Ethiopia = ETB only, no exceptions.


Rwanda = RWF only, but cross-border permitted with BNR approval.


Need multi-currency guardrail layer that checks per-country jurisdiction.


Compliance Reporting
Build Compliance Service with country-specific report templates:
Ethiopia → Quarterly (CSV/Excel).


Rwanda → Weekly (float reconciliation) + Quarterly + Annual.


Risk Governance


Rwanda explicitly requires a Data Protection Officer (DPO) and Board Risk Committee.


Ethiopia is less formalized but still expects robust risk/compliance teams.

International Standards for Fintech Compliance
We start with key international standards relevant to fintech operations, such as digital wallets, payments, remittances, lending, and merchant services. These standards ensure data security, privacy, and integrity while mitigating risks like fraud and money laundering.

Based on established frameworks, here there is a mapped core requirements of each standard to recommended technologies and implementations. These mappings draw from the solution's described capabilities (e.g., PCI-DSS-compliant architecture, tokenization, encryption, AML/CFT flows) and align with technical best practices. Implementations should be integrated into the platform's API gateway, admin console, and security modules for multi-country deployments in regions like Ethiopia and Rwanda.

The standards covered here are:

-	PCI-DSS (Payment Card Industry Data Security Standard): Focuses on protecting cardholder data.
-	GDPR (General Data Protection Regulation): Emphasizes personal data privacy and user rights.
-	ISO 27001: Provides a framework for information security management systems (ISMS).
-	AML/CFT (Anti-Money Laundering / Countering the Financing of Terrorism): Targets financial crime prevention.

Other standards like ISO 20022 (for payment messaging interoperability) or PSD2 (EU-specific open banking) are noted but not detailed here, as they are more operational than core compliance-focused for global fintech. If needed, ISO 20022 can be implemented via standardized APIs for cross-border remittances (e.g., integrating with PAPSS or SWIFT).

1. PCI-DSS Compliance
PCI-DSS ensures secure handling of payment card data, aligning with the solution's payment gateway, tokenization, and audit logs. Version 4.0.1 (effective 2025) emphasizes customized controls and strong cryptography.

Requirement	Technology/Implementation
Build and maintain a secure network (e.g., firewalls to protect cardholder data environment).	Network firewalls (e.g., Cisco ASA or cloud-native like AWS WAF); segment networks using VLANs or micro-segmentation tools like VMware NSX. Integrate with the solution's API gateway for third-party PSP integrations.
Protect stored cardholder data (e.g., do not store full PAN unless necessary).	Data encryption at rest (AES-256 via tools like AWS KMS or HashiCorp Vault); payment card tokenization (e.g., using Vault or Braintree Tokenization Service) to replace sensitive data with tokens in wallets and transactions.
Encrypt transmission of cardholder data over open networks.	TLS 1.3 encryption for all API calls and transmissions; implement HSM (Hardware Security Modules) for key management in payment engines.
Maintain vulnerability management (e.g., anti-virus and regular updates).	Endpoint protection platforms (e.g., CrowdStrike or Microsoft Defender); automated vulnerability scanners like Nessus for containerized backups and autoscaling.
Implement strong access controls (e.g., unique IDs, restrict access by need-to-know).	Role-Based Access Control (RBAC) via tools like Okta or Azure AD; integrate with biometric authentication (Face ID/Touch ID) for admin and user portals.
Regularly monitor and test networks (e.g., logging, intrusion detection).	SIEM (Security Information and Event Management) tools like Splunk or ELK Stack for real-time monitoring, alerting, and fraud analytics; conduct quarterly penetration testing with tools like Burp Suite.
Maintain an information security policy.	Policy management platforms (e.g., Vanta or Drata) for documentation; embed in DevSecOps tools for automated compliance checks during deployments.
2. GDPR Compliance
GDPR protects personal data (e.g., user profiles, transaction history), requiring privacy by design in features like eKYC, multi-currency wallets, and lending scoring. It applies extraterritorially if serving EU users or processing EU data.

Requirement	Technology/Implementation
Lawful basis for processing (e.g., obtain explicit consent).	Consent management platforms (e.g., OneTrust or Usercentrics) integrated into mobile registration and biometric login; use digital consent forms with audit trails for loan origination.
Data minimization and purpose limitation (e.g., collect only necessary data).	Data mapping tools (e.g., Collibra) to identify and limit data in wallets and remittances; implement pseudonymization in AI credit scoring to anonymize non-essential identifiers.
Integrity and confidentiality (e.g., protect against breaches).	End-to-end encryption (e.g., Signal Protocol for WhatsApp integrations); DLP tools like Symantec DLP to prevent unauthorized data exfiltration in merchant services.
Data subject rights (e.g., right to access, erasure – "right to be forgotten").	Automated DSAR (Data Subject Access Request) tools (e.g., TrustArc); integrate with admin console for profile management and deletion workflows in user accounts.
Accountability and breach notification (e.g., notify authorities within 72 hours).	Incident response platforms (e.g., PagerDuty with GDPR templates); appoint a DPO and use logging in notification engines for traceability.
Privacy by design and DPIA (Data Protection Impact Assessment).	Embed in development with tools like PrivacyTech; conduct DPIAs for high-risk features like blockchain escrow or cross-border remittances.
3. ISO 27001 Compliance
This standard establishes an ISMS for overall security, complementing the solution's DevSecOps tools, RBAC, and audit trails.

Requirement	Technology/Implementation
Risk assessment and treatment (e.g., identify threats to assets).	Risk management software (e.g., RiskWatch or RSA Archer); integrate with fraud detection in the unified admin console for ongoing wallet and transaction risks.
Access control (e.g., manage user privileges).	Identity and Access Management (IAM) systems like Okta; apply RBAC to multi-tiered accounts (users, merchants, agents).
Cryptography (e.g., secure data in transit and at rest).	Key management services (e.g., AWS KMS); align with device binding and 2FA in mobile apps.
Incident management (e.g., detect and respond to security events).	SIEM tools (e.g., Splunk) for real-time alerting; link to dispute resolution workflows.
Compliance and continual improvement (e.g., audits and reviews).	Compliance automation platforms (e.g., Vanta); use for ISO 25010 alignment in deliverables like technical specs and UAT.
4. AML/CFT Compliance
AML/CFT prevents illicit finance, integrating with KYC onboarding, transaction monitoring, and CRB integrations in the solution.

Requirement	Technology/Implementation
Customer Due Diligence (CDD/KYC) (e.g., verify identities).	eKYC platforms (e.g., Jumio or Onfido) with biometrics; tiered KYC limits in user accounts and agent portals.
Ongoing transaction monitoring (e.g., detect suspicious patterns).	AI/ML-based monitoring (e.g., ComplyAdvantage or ThetaRay); integrate with real-time FX engine and remittance corridors for anomaly detection.
Sanctions and PEP screening (e.g., check against watchlists).	Screening tools (e.g., Refinitiv World-Check); apply during onboarding and bulk disbursements.
Suspicious Activity Reporting (SAR) and record-keeping.	RegTech platforms (e.g., NICE Actimize); ensure full traceability in ledger and audit trails, with 5-10 year retention.
Risk-based approach (e.g., higher scrutiny for high-risk users).	Risk scoring engines (e.g., integrated with AI credit scoring); customize for cross-border payouts and crypto options.

These mappings provide a technical foundation for compliance. Consultants should prioritize integrations in the 6-week timeline (e.g., security in Weeks 1-2 architecture design). Next steps can include local regulations for Ethiopia (e.g., NBE Directive on Payment Systems) and Rwanda (e.g., NBR Payment System Regulations), mapping similar technologies while adapting for agent banking and payment instruments.










