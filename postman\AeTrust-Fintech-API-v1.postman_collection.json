{"info": {"_postman_id": "aetrust-fintech-api-v1", "name": "AeTrust Fintech API v1", "description": "Complete API collection for AeTrust Fintech Backend - Multi-step registration, authentication, and user management for Ethiopian and Rwandan compliance", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "aetrust-fintech"}, "item": [{"name": "Health Check", "item": [{"name": "Application Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/health", "host": ["{{base_url}}"], "path": ["api", "v1", "health"]}}, "response": []}, {"name": "Database Health", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/health/database", "host": ["{{base_url}}"], "path": ["api", "v1", "health", "database"]}}, "response": []}]}, {"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.accessToken) {", "        pm.environment.set('access_token', response.data.accessToken);", "        pm.environment.set('refresh_token', response.data.refreshToken);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"email\": \"<EMAIL>\",\n    \"password\": \"SecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/login", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "login"]}}, "response": []}, {"name": "Refresh <PERSON>", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.data && response.data.accessToken) {", "        pm.environment.set('access_token', response.data.accessToken);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"refreshToken\": \"{{refresh_token}}\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/auth/refresh", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "refresh"]}}, "response": []}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/auth/logout", "host": ["{{base_url}}"], "path": ["api", "v1", "auth", "logout"]}}, "response": []}]}, {"name": "Registration Flow", "item": [{"name": "1. Initiate Registration", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    if (response.data && response.data.registrationId) {", "        pm.environment.set('registration_id', response.data.registrationId);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"platform\": \"mobile\",\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Doe\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+251911123456\",\n    \"dateOfBirth\": \"1990-01-01\",\n    \"password\": \"SecurePassword123!\",\n    \"countryCode\": \"ET\",\n    \"intendedRole\": \"USER\",\n    \"referralCode\": \"REF123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/initiate", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "initiate"]}}, "response": []}, {"name": "2. Verify Phone Number", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/registration/{{registration_id}}/verify-phone?otpCode=123456", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "{{registration_id}}", "verify-phone"], "query": [{"key": "otpCode", "value": "123456"}]}}, "response": []}, {"name": "3. <PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/registration/{{registration_id}}/verify-email?otpCode=123456", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "{{registration_id}}", "verify-email"], "query": [{"key": "otpCode", "value": "123456"}]}}, "response": []}, {"name": "4. Submit Identity Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"registrationId\": {{registration_id}},\n    \"idType\": \"NATIONAL_ID\",\n    \"idNumber\": \"123456789012\",\n    \"idImageBase64\": \"data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD...\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/identity-verification", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "identity-verification"]}}, "response": []}, {"name": "5. Set Transaction PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"registrationId\": {{registration_id}},\n    \"pin\": \"1234\",\n    \"confirmPin\": \"1234\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/transaction-pin", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "transaction-pin"]}}, "response": []}, {"name": "6. Set Biometric Status", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"registrationId\": {{registration_id}},\n    \"biometricEnabled\": true,\n    \"biometricType\": \"fingerprint\",\n    \"deviceId\": \"device-123\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/biometric-status", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "biometric-status"]}}, "response": []}, {"name": "7. Complete Registration", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/registration/{{registration_id}}/complete", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "{{registration_id}}", "complete"]}}, "response": []}, {"name": "Get Registration Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/v1/registration/{{registration_id}}/status", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "{{registration_id}}", "status"]}}, "response": []}, {"name": "Resend Phone Code", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/registration/{{registration_id}}/resend-code?type=phone", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "{{registration_id}}", "resend-code"], "query": [{"key": "type", "value": "phone"}]}}, "response": []}, {"name": "Resend Email Code", "request": {"method": "POST", "header": [], "url": {"raw": "{{base_url}}/api/v1/registration/{{registration_id}}/resend-code?type=email", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "{{registration_id}}", "resend-code"], "query": [{"key": "type", "value": "email"}]}}, "response": []}]}, {"name": "User Management", "item": [{"name": "Get Current User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/me", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "me"]}}, "response": []}, {"name": "Update User Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"<PERSON><PERSON>\",\n    \"bio\": \"Updated bio\",\n    \"address\": \"123 Main St, Addis Ababa, Ethiopia\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/profile", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile"]}}, "response": []}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"currentPassword\": \"SecurePassword123!\",\n    \"newPassword\": \"NewSecurePassword123!\",\n    \"confirmPassword\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/password", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "password"]}}, "response": []}, {"name": "Upload Profile Picture", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "profilePicture", "type": "file", "src": []}]}, "url": {"raw": "{{base_url}}/api/v1/users/profile-picture", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "profile-picture"]}}, "response": []}, {"name": "Search Users", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}], "url": {"raw": "{{base_url}}/api/v1/users/search?query=john&page=0&size=10", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "search"], "query": [{"key": "query", "value": "john"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "Delete Account", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{access_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"password\": \"SecurePassword123!\",\n    \"reason\": \"No longer needed\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/users/account", "host": ["{{base_url}}"], "path": ["api", "v1", "users", "account"]}}, "response": []}]}, {"name": "Ethiopian Compliance Tests", "item": [{"name": "Ethiopian User Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"platform\": \"mobile\",\n    \"firstName\": \"<PERSON><PERSON>\",\n    \"lastName\": \"<PERSON><PERSON><PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+************\",\n    \"dateOfBirth\": \"1985-03-15\",\n    \"password\": \"EthiopianPassword123!\",\n    \"countryCode\": \"ET\",\n    \"intendedRole\": \"USER\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/initiate", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "initiate"]}}, "response": []}, {"name": "Ethiopian ID Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"registrationId\": {{registration_id}},\n    \"idType\": \"NATIONAL_ID\",\n    \"idNumber\": \"123456789012\",\n    \"idImageBase64\": \"data:image/jpeg;base64,ethiopian_id_image...\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/identity-verification", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "identity-verification"]}}, "response": []}]}, {"name": "Rwandan Compliance Tests", "item": [{"name": "Rwandan User Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"platform\": \"web\",\n    \"firstName\": \"<PERSON>\",\n    \"lastName\": \"Uwimana\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"+250788123456\",\n    \"dateOfBirth\": \"1988-07-20\",\n    \"password\": \"RwandanPassword123!\",\n    \"countryCode\": \"RW\",\n    \"intendedRole\": \"USER\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/initiate", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "initiate"]}}, "response": []}, {"name": "Rwandan ID Verification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"registrationId\": {{registration_id}},\n    \"idType\": \"NATIONAL_ID\",\n    \"idNumber\": \"1234567890123456\",\n    \"idImageBase64\": \"data:image/jpeg;base64,rwandan_id_image...\"\n}"}, "url": {"raw": "{{base_url}}/api/v1/registration/identity-verification", "host": ["{{base_url}}"], "path": ["api", "v1", "registration", "identity-verification"]}}, "response": []}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "base_url", "value": "http://localhost:8080", "type": "string"}, {"key": "access_token", "value": "", "type": "string"}, {"key": "refresh_token", "value": "", "type": "string"}, {"key": "registration_id", "value": "", "type": "string"}]}