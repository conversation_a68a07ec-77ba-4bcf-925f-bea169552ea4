{"id": "aetrust-fintech-dev-env", "name": "AeTrust Fintech - Development", "values": [{"key": "base_url", "value": "http://localhost:8080", "type": "default", "enabled": true}, {"key": "api_version", "value": "v1", "type": "default", "enabled": true}, {"key": "access_token", "value": "", "type": "secret", "enabled": true}, {"key": "refresh_token", "value": "", "type": "secret", "enabled": true}, {"key": "registration_id", "value": "", "type": "default", "enabled": true}, {"key": "user_id", "value": "", "type": "default", "enabled": true}, {"key": "test_email", "value": "<EMAIL>", "type": "default", "enabled": true}, {"key": "test_password", "value": "TestPassword123!", "type": "secret", "enabled": true}, {"key": "ethiopian_phone", "value": "+251911123456", "type": "default", "enabled": true}, {"key": "rwandan_phone", "value": "+250788123456", "type": "default", "enabled": true}, {"key": "test_otp", "value": "123456", "type": "default", "enabled": true}, {"key": "test_pin", "value": "1234", "type": "secret", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2024-01-01T00:00:00.000Z", "_postman_exported_using": "Postman/10.0.0"}