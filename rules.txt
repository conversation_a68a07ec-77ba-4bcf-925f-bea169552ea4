The Backend Lowdown
The Backend Lowdown
Introduction
About the Author
Who This Book Is For
Prerequisites
How to Use This Book
Structure of the book
Part I: Data Is Hard
Part II: Systems Don't Always Cooperate
Part III: Becoming a High-Leverage Backend Developer
How to approach the chapters
Read it however you need:
Use it as a reference:
Resources and tools needed
Core Tools
Optional but Helpful
Part 1
Data Is Hard
Chapter 1: Safe Migrations and Schema Changes
Why Schema Changes Are Risky
What is a Schema?
What is a Migration?
What Makes a Migration Unsafe?
Locking Large Tables
Backfilling Data at Scale
Dropping Columns or Tables Prematurely
Changing Column Types on Large Tables
Adding Non-Concurrent Indexes
Adding NOT NULL Constraints Without a Backfill
Adding Foreign Keys Without Indexes
The Common Thread
Patterns for Safe Migrations
Add Columns in Multiple Steps
Create Indexes Concurrently
Backfill in Batches, Not All At Once
Make Destructive Changes Gradually
Use Feature Flags for Behavior Changes
Avoid Renaming Columns or Tables If Possible
Add Foreign Keys with Indexes
Add the Index (in a separate, concurrent migration)
Add the Foreign Key Without Validation
Validate in a Separate Migration
Key Takeaway
Workflow Tips
Write Descriptive Migration Names and Comments
Don't Ship Code That Assumes the Migration Already Ran
Avoid Combining Multiple Changes in One Migration
Use PR Templates That Call Out Schema Changes
Run Migrations in Low-Traffic Windows
Run Schema Changes Separately From Application Deploys (When Needed)
Test Migrations with Realistic Data Locally
Monitor While You Migrate
Document Long-Running or Delayed Migrations
Know When to Ask for a Second Opinion
Observing and Deploying Migrations Safely
Set a Statement Timeout
Attempt to Make Migrations Reversible
Watch for Lock Contention
Monitor for Deployment Pipeline Failures
Keep an Eye on App Behavior During the Migration
Verify Post-Migration Success
Have a Rollback Plan - and Practice It
Use a Checklist for High-Risk Migrations
Separate Schema and App Deploys (When It Matters)
Set Expectations With the Team
"How Am I Supposed to Remember All This?"
Use Tools That Enforce Best Practices
Make Safe Migrations Part of Code Review
Document and Reuse Patterns
Conclusion
Chapter 2: Indexing, Query Planning, and Performance
What is an Index?
The Hidden Cost of Indexes
Common Ways Indexes Hurt
How to Know What to Index
Types of Indexes (Briefly)
Composite and Partial Indexes: Tools with Sharp Edges
Composite Indexes
Partial Indexes
How the Database Decides What To Do
How To Use Explain
The Plan Is a Tree
Key Things to Look At
EXPLAIN vs EXPLAIN ANALYZE
Local Isn't Good Enough
An Example Query Plan
What You Can Learn From This
Use Tools to Visualize
Get More Detail with Extended Options
Adding Indexes Safely in Production
The Problem: Locks and Downtime
The Safer Way: Use CONCURRENTLY
Safely Replacing Indexes
Best Practices for Real-World Deployments
Keep Concurrent Indexes Out of Migration Pipelines
Monitor While Building
Avoid Concurrent Indexes in High-Churn Tables During Peak Load
Tools That Help
Wrapping Up
Chapter 3: Query Pathologies, Detection and Remediation
N+1 Queries
What is an N+1 Query?
An Example
Why ORMs Make N+1s So Easy to Miss
How to Detect N+1s (Logs, Tools, Habits)
Watch Query Volume in Your Logs
Use Tools That Surface It Automatically
How to Prevent N+1s
Eager Loading Best Practices
GraphQL Lookaheads
When to Use Lookahead
Batch Patterns
A Classic Example (GraphQL-Ruby)
Where Batch Loaders Shine
Unbounded SELECTs and Cartesian Joins
Symptoms: Massive Rows, Memory Spikes
Detection: Log-Sampling, DB Metrics
Fix: Add Predicates, JOIN Filters, Explicit Projections
SELECT * & Over-Selecting Columns
Why grabbing every field kills throughput
Detection: explaining "row width" vs "heap fetches"
Remediation: ActiveRecord Patterns
Ghost Queries from Plugins/Serializers
How serializers or callbacks sneak in extra queries
Detection: SQL Tracing and Request Profiling
Fix: memoize, move logic into joins, extract to background
Pagination Performance Cliffs
What Goes Wrong
The Hidden Cost of OFFSET
Detection: Finding Your Problem Pages
Solution 1: Keyset Pagination (The Right Way™)
Solution 2: Cursor-Based Pagination (Keyset in Disguise)
Solution 3: The Hybrid Approach (Pragmatic Reality)
Solution 4: Optimizing OFFSET When You're Stuck With It
When to Use Each Approach
The Nuclear Option: Don't Paginate
Key Takeaways
Introduction
About the Author
Brief biography
My name is Steven, and I'm a software engineer with over a decade of experience building and
maintaining backend systems. I've worked across a range of environments - from enterprise Java
stacks to startup-paced Ruby monoliths - and I currently balance my love of writing software with
being a full-time dad and husband.
My experience in backend development
Over the past six years, I contributed to and ultimately led the backend architecture of a growing
eCommerce platform - first as a senior engineer, then as a tech lead operating at a staff level, driving
major system evolution and reliability efforts. While Rails shaped many of my instincts and
decisions, the lessons I've learned apply far beyond any one framework. I've dealt with everything
from painful database migrations and flaky background jobs to production fire drills, slow tests,
observability gaps, and misbehaving third-party APIs.
These aren't academic lessons - they're things I've learned the hard way, under pressure, with real
money and user trust on the line.
Why I wrote this book
There's a lot of great material on computer science and distributed systems theory. There's also
plenty of getting-started tutorials. But there's far less in between - practical, real-world advice on
what actually matters when you're building, shipping, and operating a backend in production.
This book is a collection of the habits, mental models, and debugging instincts I've built over time -
the stuff I wish I had been handed early in my career. It's not a deep-dive into protocols or
algorithms, but it will help you:
Avoid costly mistakes,
Build systems that scale with less pain,
And grow into a backend engineer others can depend on.
Whether you're working in Rails, Django, Node, Phoenix, or something else entirely, the ideas in this
book will help you write better backend code - and keep it running.
Who This Book Is For
This book is for developers who want to get better at backend engineering - not just writing code
that works, but building systems that are resilient, scalable, maintainable, and easy to debug.
The concepts in this book are designed to help you:
Avoid common backend pitfalls (locking a table during a database migration, accidentally
writing n+1 queries, etc.),
Think like an operator, not just a coder,
And level up your instincts as a backend engineer.
This isn't a book about syntax or language-specific tricks. It's about the principles, patterns, and
habits that make backend systems actually work in the real world.
If you've ever:
Fought a flaky background job,
Struggled with a slow or failing query,
Deployed something that "worked locally" and broke in prod,
Or wondered how more senior engineers always seem to know where to look when something
goes wrong...
This book is for you.
Prerequisites
You'll get the most value from this book if you already have some hands-on development experience.
Specifically, you should be comfortable with:
Web Development Fundamentals
HTTP basics, RESTful APIs, status codes, request/response cycles
Working with Databases
Understanding relational databases, SQL queries, and schema design
Programming and Debugging
Writing functions, organizing code, reading logs, using version control (Git)
Testing Basics
Writing and running unit or integration tests
APIs in Practice
You've either built or consumed APIs and have seen what happens when they break
Optional but helpful:
Experience with a backend framework like Rails, Django, Express, or Phoenix
Familiarity with CI/CD pipelines and deployment basics
Exposure to background jobs, queues, or asynchronous processing
Even if you don't check every box, don't worry - each chapter is written to be practical and
approachable. You can read it front-to-back, or jump straight to the parts that match the problems
you're facing now.
How to Use This Book
Structure of the book
This book is divided into three parts, each focused on a critical theme of backend engineering:
working with data, working with distributed systems, and becoming a more effective backend
developer.
Part I: Data Is Hard
Build data models and queries that don't break under pressure.
In this section, we focus on everything that touches your database - the lifeblood of most backend
systems. You'll learn how to:
Write queries you understand and control
Avoid performance bottlenecks like N+1s
Design safe, reversible schema migrations
Cache responsibly (and invalidate wisely)
Handle soft deletions and audit trails
Treat your schema like a shared contract with your team
Even if you're not a "database person," this section will make you one - at least enough to avoid the
most common and costly mistakes.
Part II: Systems Don't Always Cooperate
Background jobs, retries, APIs, and everything that can go wrong.
Once your app grows, you're dealing with more than just a request/response cycle - you're now
working in a distributed system, whether you planned to or not.
In this section, we explore:
How to make operations idempotent so retries don't wreak havoc
Building safe and scalable background jobs
Designing retry logic and dead-letter queues
Handling external APIs, timeouts, and webhooks
Using feature flags and kill switches for safer deploys
Monitoring and instrumentation as essential dev tools
If you've ever been paged for a failed job, this part is for you.
Part III: Becoming a High-Leverage Backend Developer
Tools, workflows, and habits that scale with you.
Being a great backend engineer isn't just about writing good code - it's about writing code that
ships, survives, and scales. This final section focuses on:
Automating repeatable tasks
Linting, annotations, and other hygiene practices
Writing tests that build confidence, not false positives
Profiling your code and test suite for real gains
Debugging systems, not just code
Thinking like a Staff+ engineer even if you're not one yet
This is where we zoom out from tactics and focus on your habits, tools, and mindset as a builder of
systems.
How to approach the chapters
This book is designed to be modular and practical - not a textbook you have to read front to back,
but a toolkit you can reach for when you're facing a real-world problem.
Read it however you need:
If you're an experienced backend developer, you'll find fresh perspectives and useful
reminders throughout. Feel free to skip around or head straight to parts about background
jobs, debugging, or system design.
If you're mid-level and looking to level up, reading in order may give you the most
structured value. We start with foundational backend concerns like schema design and query
safety before layering on system complexity and developer workflow.
If you're new to backend development, you're encouraged to go chapter by chapter. Each
one builds your intuition for building backends that don't just "work" - they survive real usage.
Use it as a reference:
Each chapter is written to stand on its own, focused on one core idea, principle, or anti-pattern.
Come back to them when:
You're preparing for a deployment and want a checklist
You're debugging a flaky background job
You're designing a schema or planning a migration
You're unsure if you should retry a failed API call
Bottom line: however you approach this book, the goal is the same - to give you the confidence,
clarity, and instincts you need to build backends that hold up when it matters most.
Resources and tools needed
You won't need to install anything just to read along - but if you want to apply the ideas, here are the
kinds of tools that will help you get the most out of it.
Core Tools
A programming language you're comfortable with
This book is language-agnostic. Whether you use Ruby, Python, Elixir, JavaScript, or Go, the
patterns and principles apply. Examples in the book will use Ruby.
A working development environment
Whatever stack you're using, you'll want:
A reliable package manager (bundler, npm, pip, etc.)
A solid way to manage language versions (asdf, nvm, pyenv, etc.)
A database running locally (PostgreSQL recommended, but not required)
A text editor or IDE you know well
VS Code, Neovim, JetBrains, Cursor - use whatever helps you move quickly and think clearly.
A terminal you're comfortable with
Backend work often involves CLI tools, scripting, and log tailing. Use what works best on your
OS (e.g. iTerm2, Warp, Windows Terminal).
Git
All examples and workflows in this book assume you're using Git for version control.
Optional but Helpful
A test framework
Unit and integration testing come up often. Pick a test runner that works well with your stack.
A job queue / background worker
To experiment with async patterns, a tool like Sidekiq, Celery, BullMQ, or Oban can help.
A local observability setup
Tools like lograge, statsd, OpenTelemetry, or simple log formatting make a big
difference once you start debugging distributed behavior.
You don't need to install any of these tools to benefit from this book - but having a working backend
project (even a toy one) will help you translate the principles into practice.
Part 1
Data Is Hard
Chapter 1: Safe Migrations and
Schema Changes
Your database schema is the backbone of your application - and changing it is one of the most
dangerous things you can do.
It starts innocently enough: you need a new column, or you want to enforce a constraint. Maybe
you're cleaning up an old table. But in a live system, every schema change runs the risk of locking
tables, blocking queries, breaking code, or even taking down production entirely.
If you've ever merged a migration and then watched your deploy hang, you already know this. If you
haven't - good. Let's keep it that way.
This chapter focuses on the practical side of schema evolution:
What makes migrations dangerous (and why it often isn't obvious)
Patterns and workflows for making schema changes safely
How to monitor, validate, and deploy migrations with confidence
The good news? Most migration-related incidents are entirely preventable. With a few habits, tools,
and mental models, you can change your database with confidence - even under real production
load.
Why Schema Changes Are Risky
If you're building or maintaining a backend system, database schema changes are inevitable. Over
time, your application will evolve - new features are introduced, data models are rethought, and
assumptions get replaced. These shifts often require corresponding changes to the schema: adding
columns, updating indexes, renaming tables, or removing no-longer-needed fields.
In an ideal world, these changes would be routine and uneventful. But in real systems, schema
changes are one of the most common sources of production issues. A poorly planned migration can:
Lock a critical table and block traffic,
Corrupt or delete data,
Stall or break your deployment process,
Or even take your entire app offline.
Most developers learn this the hard way.
Maybe you added a column with a NOT NULL constraint to a table with millions of rows, and it
locked the database. Or maybe you dropped a column you thought was unused, only to find a
background job still depending on it. Schema changes are risky not just because of what they alter,
but because of the tight coupling between your application code and your data's structure.
Making safe schema changes isn't just about writing correct SQL - it's about making those changes
without surprising your database, your app, or your users.
In the rest of this chapter, we'll cover:
What schema changes actually are,
The common mistakes that cause real-world incidents,
Practical patterns to make migrations safe,
And tools that can help you catch dangerous changes before they ship.
The goal isn't to make you afraid of schema changes - it's to help you respect them, plan them, and
execute them with confidence.
What is a Schema?
When we talk about a database schema, we're talking about the structure of your data - the tables,
columns, types, constraints, and relationships that define how information is stored and organized.
If your application is the interface users interact with, the schema is the contract that ensures data
flows in and out in a predictable, reliable way.
It includes things like:
The tables you've defined (e.g., users, orders, products)
The columns on those tables and their types (e.g., email as TEXT, created_at as
TIMESTAMP)
Primary keys, indexes, and foreign key relationships
Constraints like NOT NULL, UNIQUE, or CHECK
Example of a simple schema:
USERS
UUID id PK
string email
timestamp created_at
CARTS
UUID id PK
UUID user_id FK
timestamp created_at
timestamp checked_out_at
CART_PRODUCTS
UUID id PK
UUID cart_id FK
UUID product_id FK
integer quantity
PRODUCTS
UUID id PK
string name
decimal price
boolean in_stock
owns
contains included_in
Most ORMs (Object-Relational Mappers) like ActiveRecord, Ecto, and Django's ORM abstract this
away to some degree. You write code that defines models, and the ORM takes care of translating that
into schema changes. But regardless of how you write it, the schema still exists at the database level,
and the application depends on it being correct.
Even small decisions about your schema can have outsized consequences. The types you choose can
affect performance and compatibility. The constraints you apply (or don't) shape what data your
system accepts. The naming of tables and columns becomes part of your domain language - for
developers, for users, and even for your analytics teams.
Your schema is more than just a technical artifact - it's the foundation of your system's data
integrity, performance, and clarity.
What is a Migration?
A migration is a way to change your database schema over time. It's a set of instructions - often
written in SQL or wrapped by a framework - that modifies your database's structure: adding or
removing tables, columns, indexes, constraints, or even entire relationships.
Think of migrations as version control for your schema. Just like you commit code changes to
Git, you write and apply migrations to evolve your database safely and incrementally.
Some common types of migrations include:
Adding a new table (CREATE TABLE)
Adding or removing columns (ALTER TABLE)
Creating or dropping indexes (CREATE INDEX, DROP INDEX)
Enforcing constraints like NOT NULL, UNIQUE, or FOREIGN KEY
Renaming or changing the type of existing fields
Most modern frameworks give you tools to write migrations in code and apply them in a consistent
order. For example:
Rails uses ActiveRecord!"Migration
Django uses makemigrations and migrate
Ecto (Elixir) has mix ecto.migrate
Sequelize (Node.js) and Alembic (Python) offer similar support
Behind the scenes, these migrations are just SQL. And whether you write them by hand or use a tool
to generate them, the outcome is the same: your schema changes in a controlled, versioned way.
Important
The key word is controlled. A migration doesn't just describe what to change - it executes that
change, often on a live production database. That's why it's so important to understand what's
happening under the hood.
Migrations are powerful - they let your schema evolve alongside your code. But they also have sharp
edges. The rest of this chapter is about learning to use them safely.
What Makes a Migration Unsafe?
Not all migrations are created equal. Some are quick and harmless. Others can lock your most
important tables, block traffic, spike I/O, and even take down production. The difference usually
comes down to how much data is affected, when you run the migration, and how much
traffic is hitting your database at the time.
Warning
A dangerous migration might go unnoticed in staging or a low-traffic app - but the same
change in production, under load, can cause outages.
Let's break down the most common causes of unsafe migrations - and why they're risky.
Locking Large Tables
Certain schema changes force the database to acquire an exclusive lock on a table. During that
time:
Reads and writes are blocked from other sessions,
Any queued queries pile up,
Application threads start timing out,
And eventually, users see errors or your workers hang.
This is how seemingly innocent schema changes can bring down production.
Example:
Note
The rest of the examples will only contain the lines inside the change method for brevity,
unless stated otherwise.
class AddStatusToUsers < ActiveRecord!"Migration[8.0]
def change
add_column :users, :status, :string, null: false
end
end
This locks the entire users table while it:
Adds the column,
Applies the NOT NULL constraint,
And rewrites every row (because of the constraint).
On a high-traffic table, even a few seconds of blocked writes can trigger a chain reaction. As the
migration holds a lock, incoming write queries start to pile up - and each one consumes a database
connection while it waits. Before long, your app's connection pool fills up, and any new request
that needs a DB connection is forced to wait.
Workers begin to hang as they time out waiting for connections. Requests start to fail. If this
happens during a deploy, your health checks may fail too - which can trigger a deploy rollback or
leave your app in a partially updated state. All of this can unfold in under a minute if the table is
busy enough.
Backfilling Data at Scale
If you add a new column and want to populate it based on existing data, it's tempting to do it inline:
Example:
This triggers a single massive write across all rows, which can:
Saturate I/O: flood your disk with writes
Lock rows: prevent other updates to the same table
Trigger timeouts: if the operation exceeds your query timeout, it can kill the transaction
Dropping Columns or Tables Prematurely
Removing a column may seem like harmless cleanup - but if any code still references it, you'll
break your app.
This includes:
# This will touch every row in the table at once
User.update_all(status: "active")
Background jobs
Cron tasks
Third-party integrations
Admin dashboards
Even stale feature flags or A/B tests
Changing Column Types on Large Tables
Changing a column's type often requires a full table rewrite. That means the DB must rewrite
every row and may hold locks throughout the process.
Example:
This can:
Lock the table
Take a long time
Fail partway through and roll back
Adding Non-Concurrent Indexes
Creating an index helps with performance - but on large tables, the default method can block all
writes while the index is built. That's because the database needs to ensure index consistency
during creation.
Dangerous:
This will block INSERTs, UPDATEs, and DELETEs until the index is built.
Adding NOT NULL Constraints Without a Backfill
Adding a new column with both a NOT NULL constraint and a DEFAULT value might seem safe -
but on large or high-traffic tables, it can be deceptively dangerous.
change_column :orders, :total_cents, :bigint
add_index :users, :email
Dangerous:
This will succeed even if the table already has rows, because PostgreSQL will backfill the new
column with the default. But here's the catch: PostgreSQL performs a full table rewrite to do
this. That means:
Every row is updated and written to disk
The entire table is locked for writes during the operation
On large tables, this can cause significant downtime or deploy failures
Adding Foreign Keys Without Indexes
Adding a foreign key to a high-traffic table without also adding an index can tank performance -
especially for deletes or updates.
Why?
 The DB has to validate the constraint across all related rows, and without an index, this becomes
a slow sequential scan.
The Common Thread
Unsafe migrations often fail in one of three ways:
They lock more than you expect
They take longer than you expect
They change more than your app is ready to handle
Even experienced teams make these mistakes - especially under time pressure. But with a few
patterns and some lightweight tools, you can avoid most of them entirely.
Let's walk through those patterns next.
Patterns for Safe Migrations
add_column :users, :status, :string, null: false, default: "active"
Now that we've covered how migrations can go wrong, let's talk about how to make them go right.
Most safe migrations boil down to a few key principles:
Avoid locking large tables
Break changes into multiple steps
Backfill data carefully
Avoid assumptions about data shape
Use the database's features to your advantage
Here are some common safe patterns, with real examples and tradeoffs explained:
Add Columns in Multiple Steps
Never add a column with a NOT NULL constraint and a default in a single migration. This forces the
database to backfill and rewrite the entire table in one transaction - locking it.
Bad:
Good:
add_column :users, :status, :string, null: false, default: "active"
# Step 1 - Add as nullable, no default
add_column :users, :status, :string
# Step 2 - Backfill in batches
User.find_in_batches do |batch|
batch.each { |user| user.update_columns(status: "active") }
end
# Step 3 - Add NOT NULL constraint
change_column_null :users, :status, false
# Optional Step 4 - Set default for new rows
change_column_default :users, :status, "active"
This avoids locking, gives you control over the backfill, and lets you verify before locking in
constraints.
Create Indexes Concurrently
Creating indexes on large tables without concurrency will block writes until the index is done.
Safe ActiveRecord pattern for Postgres:
This avoids blocking but requires:
No wrapping transaction
Careful migration ordering (you can't combine this with other schema changes)
Backfill in Batches, Not All At Once
Large-scale updates can trigger row locks, long-running transactions, and excessive disk I/O.
Safe pattern:
You can also throttle the backfill with sleep if necessary.
Make Destructive Changes Gradually
Dropping a column should be the last step in a cleanup - not the first.
Safe approach:
1. Stop using the column in code
disable_ddl_transaction! # note: this line goes outside the `class`
add_index :orders, :created_at, algorithm: :concurrently
User.find_in_batches(batch_size: 1000) do |batch|
batch.each do |user|
user.update_columns(status: "active")
end
end
2. Mark it ignored in your ORM (e.g. self.ignored_columns)
3. Deploy and monitor
4. Drop the column in a later migration
This lets you surface unexpected usage before it becomes a production incident.
Use Feature Flags for Behavior Changes
If your migration enables new behavior - like a new enum value or a changed default - guard that
logic behind a feature flag or runtime toggle.
This lets you:
Deploy the schema independently of the code
Roll back without undoing the migration
Test incrementally before committing fully
Avoid Renaming Columns or Tables If Possible
Renaming breaks introspection tools, admin dashboards, and analytics scripts. If you must rename,
consider:
Adding the new column
Backfilling
Migrating reads/writes
Dropping the old one in a later deploy
Same goes for table names - you often want to create a new one and copy data over gradually.
Add Foreign Keys with Indexes
Adding a foreign key constraint can be deceptively dangerous - especially on large tables. By default,
the database will:
Scan the referencing table to validate existing data,
Lock it while doing so,
And block concurrent writes during the process.
To avoid these problems, follow a two-step migration strategy: add the constraint without
validation, then validate it in a later, isolated migration. Also, ensure the foreign key column is
indexed concurrently before you add the constraint - otherwise, performance can suffer on
deletes, updates, or validation scans.
Add the Index (in a separate, concurrent migration)
Add the Foreign Key Without Validation
This prevents the foreign key from locking the orders table while it scans for invalid rows.
Validate in a Separate Migration
Once the above has shipped safely, you can validate the constraint in a dedicated migration:
disable_ddl_transaction!
class AddUserIdIndexToOrders < ActiveRecord!"Migration[8.0]
def change
add_index :orders, :user_id, algorithm: :concurrently
end
end
class AddForeignKeyOrdersUsers < ActiveRecord!"Migration[8.0]
def change
add_foreign_key :orders, :users, validate: false
end
end
class ValidateOrdersUserIdFk < ActiveRecord!"Migration[8.0]
def change
validate_foreign_key :orders, :users
end
end
This lets you schedule the validation during a low-traffic window without blocking app writes or
risking a deploy timeout.
Key Takeaway
You don't get extra points for doing it all in one migration.
Safe migrations are incremental, observable, and reversible. Don't aim for clever - aim for boring and
survivable.
Workflow Tips
Even safe migrations can go sideways if they're poorly timed, rushed, or not coordinated. Schema
changes are as much a team process as they are a technical task - and the way you structure your
workflow can dramatically reduce risk.
Here are some practical habits that make migrations safer, easier to deploy, and less likely to surprise
anyone at 2AM:
Write Descriptive Migration Names and Comments
Naming your migration AddStatusToUsers is fine, but leaving comments inside the file
explaining why you're doing something is even better - especially if it's a workaround or involves
delayed backfilling.
Example:
Your future teammates (or future you) will thank you.
Don't Ship Code That Assumes the Migration Already Ran
# Adding this as nullable first to allow backfill via background job.
# Once all rows are populated, we'll add NOT NULL in a follow-up migration.
add_column :users, :status, :string
If your application code expects a column or table that hasn't been deployed yet, it can break
production if the app deploys before the migration runs - or if the migration fails halfway through.
Safer pattern:
Deploy schema changes first
Ensure they succeed
Then deploy the code that depends on the new schema
This requires separating schema and code deploys when they're tightly coupled - or using runtime
conditionals (e.g., feature flags, presence checks).
Avoid Combining Multiple Changes in One Migration
It might feel cleaner to bundle several schema changes together, but it makes it harder to:
Spot which change introduced a problem
Revert the migration cleanly
Roll out partial schema changes in a controlled way
Tip
One concern per migration. Let your migration history tell the story clearly.
Use PR Templates That Call Out Schema Changes
Include a section in your pull request template that explicitly asks:
Are there any schema changes in this PR?
Have you reviewed the query plan?
Will this block large tables or cause downtime?
This invites peer review and catches accidental footguns early.
Run Migrations in Low-Traffic Windows
Even safe migrations benefit from being run when traffic is low:
Less contention on locks
Easier to monitor impact
Safer to roll back quickly
If you're on a global app, define your "quiet hours." If you're B2B SaaS, Sunday mornings might be
ideal.
Tip
Use database replicas to measure traffic patterns if you're not sure when peak vs off-peak
occurs.
Run Schema Changes Separately From Application Deploys (When
Needed)
This is especially important when:
Your code depends on the schema being present
Your migration disables or replaces columns/tables
You're using algorithm: :concurrently (which can't be inside a transaction)
Consider adding schema deploys as a separate CI/CD step with their own monitoring and alerts.
Test Migrations with Realistic Data Locally
It's easy to be misled by a migration that runs instantly on an empty dev DB.
Use:
A production-like dataset locally
Rails' db:structure:load + seed scripts
Tools like pg_dump or staging snapshots
This gives you a better sense of how long the migration takes and whether it might lock tables
unexpectedly.
Monitor While You Migrate
If your migration touches critical tables or data, monitor:
DB lock times
Write queue length
Deployment pipeline status
Error rates across the app
Tip
Have a rollback plan ready - and know how to run it before you need to.
Document Long-Running or Delayed Migrations
If a migration has a follow-up (e.g. adding a NOT NULL later), track it in a shared doc, Jira ticket, or
README in the migrations folder. Schema drift happens fast on growing teams - especially when
people assume the work is already done.
Know When to Ask for a Second Opinion
If you're touching a high-traffic table, trying something new (like a partial index), or just not sure
how risky something is - ask another engineer to review the plan. It's easier to debug a migration you
haven't shipped yet.
Observing and Deploying Migrations Safely
Once you've written a migration and planned the rollout, your job isn't over. A migration that works
in staging can still misbehave in production - especially under load or with unexpected data.
To minimize risk, treat schema changes like any other deploy: something to observe, verify, and
potentially roll back. Here's how to do that effectively.
Set a Statement Timeout
Always set a statement_timeout when running migrations in production. This prevents a
runaway or blocking migration from hanging indefinitely and causing collateral damage (e.g. stalled
deploys, locked tables, or frozen workers).
PostgreSQL example:
In Rails, you can wrap your migration logic like this:
This forces the migration to fail fast if it takes longer than expected - which is often better than
letting it quietly lock things for minutes.
Attempt to Make Migrations Reversible
Whenever possible, write migrations that are reversible - so you (or a future teammate) can undo
them safely.
ActiveRecord supports reversible migrations automatically in many cases:
But if your migration involves raw SQL or irreversible actions (like drop_table or destructive
execute calls), use the up/down form and at least attempt to write a rollback:
Even if it's not a perfect reversal, making a best-effort rollback is better than leaving someone
stranded.
SET statement_timeout = '15s';
def up
execute "SET statement_timeout = '15s';"
# !!$ rest of the migration
end
def change
add_column :users, :status, :string
end
def up
remove_column :users, :status
end
def down
add_column :users, :status, :string
end
Watch for Lock Contention
Before running a migration - especially on large or high-traffic tables - it's important to check
whether your database is already under lock pressure, also known as lock contention.
Note
Lock contention happens when multiple database operations are trying to acquire conflicting
locks on the same resource (like a table or row), and some of them have to wait. This can slow
down queries, cause timeouts, or block your migration entirely.
If your migration needs to lock a table - even briefly - and another long-running query is already
holding a conflicting lock, your migration will just sit there waiting. Worse, if other queries pile up
behind it, you can get a cascading slowdown or even an outage.
That's why it's good practice to check for:
Long-running queries - may be holding locks that your migration will conflict with
Autovacuum activity - can hold lightweight locks, especially if it's vacuuming a large or
bloated table
Active transactions - especially those that have been open for a long time
You can check for ungranted (i.e. waiting) locks with:
Or tools like:
pg_stat_activity - to see current queries and lock wait status
Your APM (New Relic, Datadog, etc.) - to catch rising DB latency or blocked transactions
App-level logs (e.g. Rails + Lograge) - to see if requests are slowing down or queueing
If you see signs of contention, it's best to wait, monitor, or reschedule your migration for a lowertraffic time - like off-peak hours or a dedicated maintenance window.
Monitor for Deployment Pipeline Failures
If your migration runs inside a CI/CD pipeline step, and that migration blocks for too long (e.g. on a
lock), your entire deploy can get stuck - potentially leading to:
SELECT * FROM pg_locks WHERE NOT granted;
Autoscaling delays
Worker exhaustion
Connection pool saturation
Always have a way to:
Cancel a stuck deploy
Apply schema changes out-of-band (e.g. via a direct console, heroku run, etc.)
Keep an Eye on App Behavior During the Migration
While a migration is running:
Are API response times spiking?
Are job queues backing up?
Are user-facing features failing silently?
Dashboards that track:
DB query throughput
Error rates
95th/99th percentile latencies
...can give you early warning signs.
Verify Post-Migration Success
After the migration finishes, verify:
The column or index was created as expected
Backfilled data is present (if applicable)
Code that uses the schema is behaving normally
No background jobs are crashing due to missing fields
Tip
Have a checklist (or a Slack thread) for this step. It forces you to confirm instead of just
assuming it worked.
Have a Rollback Plan - and Practice It
Rolling back code is easy. Rolling back a schema change? Not so much.
Adding a column: Easy to ignore
Dropping a column or table: Hard (may need data recovery)
Adding a constraint or index: May need to DROP and retry later
Renaming a column: Often not reversible without a copy + migration
Tip
Best practice: Write the down method (or reverse SQL) at the same time you write the
migration.
Use a Checklist for High-Risk Migrations
For critical migrations, run through a quick checklist:
Is this table high-traffic?
Will this lock the table?
Did I test it with real data?
Can I deploy it separately from code?
Do I know how to undo it?
Do I know who to ping if something breaks?
Simple rituals like this can prevent painful incidents.
Separate Schema and App Deploys (When It Matters)
When deploying a schema change that is required by your application code (e.g. your app now
depends on a new column being present), deploy the schema first, verify it worked, then deploy the
application code.
This separation:
Avoids boot-time crashes
Keeps deploys smaller and more focused
Gives rollback flexibility
Set Expectations With the Team
Let others know when you're running a schema migration:
Warn people ahead of time (especially ops / SRE)
Share the migration plan and expected impact
Post success/failure status in Slack or your deployment channel
Schema changes are invisible to most of your teammates - don't let them become invisible and
dangerous.
"How Am I Supposed to Remember All This?"
If you're feeling overwhelmed - you're not alone.
It's easy to look at everything that can go wrong with schema changes and think, "How does anyone
manage to do this safely in real life?"
The truth is: you're not expected to memorize every edge case. You're expected to build
processes and use tools that catch the most dangerous patterns before they hit production.
Here are some ways to make safe migrations a habit, not a headache:
Use Tools That Enforce Best Practices
As an example, the Rails community has the following gems:
strong_migrations
Raises exceptions when you write a migration that might cause downtime (e.g. adding a NOT
NULL column, or creating a non-concurrent index).
online_migrations
A more comprehensive tool for Rails, with built-in checks, safety suggestions, and automatic
generation of safer migration steps.
These tools act like a guardrail - they don't stop you from making changes, but they do stop you from
doing them dangerously.
Make Safe Migrations Part of Code Review
Include a section in your pull request template or code review checklist that asks:
Are there any schema changes in this PR?
Have they been tested with real data?
Will they lock or rewrite a large table?
When schema changes become part of your team's shared review culture, they stop being scary - and
start being just another form of engineering rigor.
Document and Reuse Patterns
If your team runs similar migrations repeatedly (e.g. adding audit columns, changing enums,
backfilling state), create a "migration cookbook" in your docs or wiki.
Reusable scripts, checklists, and examples will save time and reduce mistakes.
Safe migrations aren't about perfection - they're about process. And with the right patterns, tools,
and habits, you'll be able to make changes with confidence.
Conclusion
Schema changes are one of the most powerful - and dangerous - tools in a backend engineer's
toolkit. Done well, they keep your system evolving, your models accurate, and your data clean. Done
carelessly, they can lock up your database, stall deployments, or take down production entirely.
This chapter wasn't meant to scare you - it was meant to prepare you.
We've covered:
Why schema changes are risky (and how those risks show up in real systems),
What makes certain migrations unsafe,
Patterns and best practices for writing safe migrations,
How to structure your workflow to reduce risk,
How to monitor and validate migrations during deployment,
And tools that help you catch mistakes early - before they become incidents.
If there's one habit to take away, it's this: treat your schema changes like production code. Test
them, monitor them, document them, and assume they'll run under pressure - because they will.
Chapter 2: Indexing, Query Planning,
and Performance
When backend systems feel slow, it's often not your code - it's your queries.
A single missing index or misunderstood filter can silently drag your system down, especially under
real-world data. And unless you know how to spot it, you'll be left guessing.
In this chapter, we'll focus on building your intuition around how queries interact with your
database, and how to figure out why a query is slow when it shouldn't be. You'll learn:
What indexes actually are (and why they matter)
How the database planner decides whether or not to use one
How to read an EXPLAIN ANALYZE plan to diagnose slow queries
How to add an index without taking down production
You don't need to be a database expert. But if you're working in the backend, learning to read query
plans is one of the highest-leverage skills you can develop.
What is an Index?
An index is a data structure the database uses to find rows faster - like a shortcut to avoid scanning
every row in a table. If you've ever used a book index to jump straight to a topic, you already get the
idea.
Without an index, a query like:
requires the database to scan every row in the users table to see if email matches. This is called a
sequential scan, and it works - but it's slow at scale.
With an index on email, the database can jump directly to the rows it needs, skipping
everything else. That can turn a query that takes seconds into one that runs in milliseconds.
The Hidden Cost of Indexes
Indexes can dramatically improve read performance - but they're not free. In fact, they're one of the
easiest ways to accidentally hurt your system's performance while thinking you're helping. Here's
how that happens, and how to avoid it.
Common Ways Indexes Hurt
They slow down writes.
Every time you insert, update, or delete a row, the DB has to update every relevant index. The
more indexes you have, the more work each write operation does.
They take up disk space.
Especially on wide tables or large datasets, indexes can significantly bloat your storage - and
backups.
They can mislead the query planner.
Too many indexes (especially overlapping or misaligned ones) can confuse the planner and
lead to suboptimal execution paths.
They slow down bulk operations.
Large INSERTs, batch updates, or data migrations are often much slower with indexes in
place. This is why it's common to temporarily drop indexes before importing millions of rows.
SELECT * FROM users WHERE email = '<EMAIL>';
How to Know What to Index
Start from the query
Don't guess - use EXPLAIN ANALYZE on your slowest queries to see what the database
actually needs.
Only index columns used in filters or joins
Indexes help most on columns used in WHERE, JOIN, and sometimes ORDER BY.
Watch out for low-cardinality fields
Indexing columns like archived or status rarely helps on their own - unless paired with
another condition.
Keep multicolumn indexes lean and purposeful
Column order matters. Indexes on (user_id, created_at) won't help if your query only
filters by created_at.
Skip indexes during large imports
For big INSERT jobs, drop non-essential indexes first and recreate them afterward to speed
things up.
Types of Indexes (Briefly)
Most of the time, when you create an index, you're creating a B-tree index - a balanced tree
structure that works well for exact lookups, ranges, and sorting.
But databases support other types too, like:
Hash indexes – optimized for exact matches
GIN (Generalized Inverted Indexes) – great for:
Full-text search
Array columns
jsonb columns - especially when you query nested keys or elements inside a JSON blob
BRIN (Block Range Indexes) – useful for massive, append-only tables where data is
naturally ordered (e.g. logs, time series data)
In this chapter, we'll mostly focus on B-tree indexes, because they're the default and cover 90% of
what you need to know. But knowing that other types exist - especially GIN for JSONB - can save
you when your schema gets more complex.
Note
Index Use Isn't Guaranteed
Even if you have an index, Postgres might ignore it. The planner makes a judgment call based
on cost estimates - sometimes wrong! Tools like EXPLAIN ANALYZE help you understand
what it actually chose.
Composite and Partial Indexes: Tools with Sharp
Edges
When a single-column index doesn't cut it, you've got two advanced options: composite and partial
indexes. These aren't exotic - they're powerful, common tools that can dramatically improve
performance if used well. But they come with tradeoffs.
Keep them in your toolbox - just don't reach for them blindly.
Composite Indexes
A composite index covers multiple columns, in a specific order:
This index is useful when your query filters or sorts by both columns - or when it starts with
user_id. But here's the catch:
The order matters.
Postgres can use this index for:
WHERE user_id = ?`
WHERE user_id = ? AND created_at > ?
But not for:
CREATE INDEX ON orders (user_id, created_at);
WHERE created_at > ? (by itself)
Warning
Composite indexes often get created based on a single query, but then go unused because other
queries filter on the columns in a different order.
Partial Indexes
A partial index applies to only a subset of the table, based on a condition:
Used well, they're lightweight and fast - perfect when you only care about a slice of your data.
Use case: You often query only active users, and don't need an index that covers suspended ones.
Pitfall #1:
Your query must exactly match the predicate (WHERE suspended = false) - otherwise the
index won't be used.
Pitfall #2:
Stats and usage tracking (pg_stat_user_indexes) may be misleading - if the predicate isn't
common, it may show up as "unused" even when it's working exactly as intended.
How the Database Decides What To Do
Before your query runs, Postgres doesn't just dive in blindly - it pauses and asks: What's the most
efficient way to do this?
That decision is made by the query planner, which looks at:
The structure of your query
The available indexes
Freshness of table statistics
Estimated row counts and costs
CREATE INDEX ON users (last_login_at) WHERE suspended = false;
If an index exists on the filtered column, and the planner believes using it is cheaper than scanning,
it'll take the index path. If not - say, because of outdated stats or low selectivity - it may choose a
sequential scan, examining rows one by one.
This plan becomes the blueprint for how the database will fetch and process your data.
The planner evaluates multiple execution strategies - like using an index, scanning the whole table,
or joining two tables in different ways - and chooses the one it thinks will be cheapest based on what
it knows about the data. That decision depends on:
Whether an index exists on the columns in your WHERE, JOIN, or ORDER BY
How selective your query is (does it filter 5 rows or 5 million?)
How many rows it expects to scan, join, or sort
Sometimes it picks correctly. Sometimes it guesses wrong - especially if table statistics are stale or
the query is more complex than it looks.
Note
The database bases these decisions on table statistics - internal metadata about row counts,
column value distributions, and more.
These stats are automatically updated by Postgres during VACUUM, ANALYZE, and
AUTOANALYZE operations.
But if they're stale (e.g. after a bulk insert or delete), the planner may make bad guesses and
choose inefficient plans.
To understand what the planner actually chose (and why), you use EXPLAIN. EXPLAIN generates a
breakdown of how the database plans to execute your query - including which indexes (if any) it will
use, what join strategies it will apply, how many rows it expects to process at each step, and how
costly each operation is expected to be. And once you know how to read it, you can start spotting
when it's choosing poorly - and how to guide it toward better plans.
How To Use Explain
When you prepend a query with EXPLAIN, the database doesn't execute it - instead, it shows you the
execution plan: a step-by-step blueprint of how it would run the query. Each line in the plan
represents an operation the database will perform, in order, from the innermost data access to the
final output.
Here's a basic example:
The output might look something like:
Let's break it down.
The Plan Is a Tree
Even though it's shown top-to-bottom, the plan is actually a tree of operations - each one feeding
into the next. For simple queries, it's often just one step. For complex joins or subqueries, you'll see
multiple nested levels.
Key Things to Look At
Scan Type
This tells you how the database is accessing the data:
Seq Scan means a full-table scan - often slow on large tables.
Index Scan or Index Only Scan means it's using an index - usually good.
Bitmap Heap Scan means it's using part of an index, but still hitting the heap.
Index Used
Shown in lines like Index Scan using index_users_on_email. Confirms which
index the planner chose.
Filter or Condition
Index Cond: or Filter: shows the condition being applied. Sometimes, an index exists
but isn't used because the condition doesn't match it exactly.
EXPLAIN SELECT * FROM users WHERE email = '<EMAIL>';
Index Scan using index_users_on_email on users (cost=0.29!%8.30 rows=1
width=100)
Index Cond: (email = '<EMAIL>')
Cost
Displayed as cost=START!%END. It's not in seconds - it's a relative estimate of work,
useful for comparing steps. Lower is better.
Rows Estimate
The planner's guess of how many rows this step will produce. If it's wildly off from reality,
your statistics may be stale.
EXPLAIN vs EXPLAIN ANALYZE
It's important to understand the difference:
EXPLAIN shows what the database plans to do
EXPLAIN ANALYZE actually runs the query and shows what really happened
Example:
This gives you:
Actual timing (how long each step took)
Actual row counts (vs estimated)
Whether the plan was efficient or not
Caution
EXPLAIN ANALYZE executes the query - which means it can write to or delete data if
used with INSERT, UPDATE, or DELETE. Be careful, especially on production. Even if you're
running a SELECT statement, it could still impact the database's performance if the query is
slow!
Local Isn't Good Enough
If you want to understand real performance, you need to run EXPLAIN on a production-like
database. Local databases usually don't reflect:
The volume of data (your local table might have 50 rows, prod has 5 million)
EXPLAIN ANALYZE SELECT * FROM users WHERE email = '<EMAIL>';
The shape of access patterns (no concurrency, no cache pressure)
Accurate table statistics (which are updated via VACUUM, ANALYZE, or AUTOANALYZE in
production)
A query that uses an index locally might full-table scan in prod. Or vice versa. Always test on data
that resembles reality.
An Example Query Plan
Let's look at an example query plan taken straight from a running application. We want to find the
most recently active users in the last 30 days and see how many vocabulary words each has
associated.
Which results in this query plan:
EXPLAIN ANALYZE
SELECT users.id, users.email, COUNT(DISTINCT vw.word_id) AS
unique_words_saved
FROM users
JOIN oauth_access_tokens oat
ON oat.resource_owner_id = users.id
LEFT JOIN vocabulary_words vw
ON vw.user_id = users.id
WHERE oat.created_at > NOW() - INTERVAL '30 days'
GROUP BY users.id, oat.created_at
ORDER BY oat.created_at DESC
LIMIT 10;
Limit (cost=46.67!%46.75 rows=4 width=59) (actual time=0.354!%0.370 rows=9
loops=1)
!& GroupAggregate (cost=46.67!%46.75 rows=4 width=59) (actual
time=0.354!%0.368 rows=9 loops=1)
Group Key: oat.created_at, users.id
!& Sort (cost=46.67!%46.68 rows=4 width=67) (actual
time=0.344!%0.346 rows=22 loops=1)
 Sort Key: oat.created_at DESC, users.id
 Sort Method: quicksort Memory: 27kB
Here's what's happening, from the bottom up:
Seq Scan on oauth_access_tokens: Postgres scans the entire
oauth_access_tokens table looking for rows created in the last 30 days. This filter
removes 959 rows, leaving 9. Because there's no index on created_at, it can't avoid the full
scan.
Seq Scan on users: For each of the 9 recent tokens, it loops over the entire users table
(42 rows each time). A nested loop join is used since the dataset is small.
Index Only Scan on vocabulary_words: For each user, it checks for associated
vocabulary words using a preexisting index. Postgres doesn't even have to fetch rows from disk
(Heap Fetches: 0) because the index contains everything it needs.
Sort: The results are sorted by most recent activity. Memory usage is low (~27kB) and
quicksort is used.
!& Nested Loop Left Join (cost=0.27!%46.63 rows=4 width=67)
(actual time=0.245!%0.323 rows=22 loops=1)
!& Nested Loop (cost=0.00!%45.60 rows=1 width=51)
(actual time=0.240!%0.297 rows=9 loops=1)
Join Filter: (users.id = oat.resource_owner_id)
 Rows Removed by Join Filter: 371
!& Seq Scan on oauth_access_tokens oat
(cost=0.00!%41.36 rows=1 width=24) (actual time=0.226!%0.229 rows=9 loops=1)
 Filter: (created_at > (now() - '30
days'!"interval))
 Rows Removed by Filter: 959
!& Seq Scan on users (cost=0.00!%3.55 rows=55
width=43) (actual time=0.001!%0.005 rows=42 loops=9)
!& Index Only Scan using
index_vocabulary_words_on_user_id_and_word_id on vocabulary_words vw
(cost=0.27!%0.86 rows=17 width=32) (actual time=0.002!%0.002 rows=2 loops=9)
Index Cond: (user_id = users.id)
 Heap Fetches: 0
Planning Time: 0.350 ms
Execution Time: 0.428 ms
GroupAggregate: Aggregates the word counts per user. We grouped on both users.id
and oat.created_at (though in an app with more users, you'd likely group only by
users.id or just use the most recent created_at).
Limit: Finally, Postgres returns only the top 10 results after sorting.
What You Can Learn From This
Even small datasets can trigger complex plans.
Nested loop joins are fine with small row counts but dangerous at scale.
Index Only Scan is a powerful optimization - but it only works if the index includes all
the needed data and Postgres knows the heap doesn't need to be checked (thanks to
autovacuum).
You can see exactly how many rows are filtered and how long each step takes.
Use Tools to Visualize
EXPLAIN output is readable, but not always intuitive - especially for large or nested queries.
Use a tool like Dalibo's EXPLAIN Visualizer to turn raw query plans into interactive trees. Just
copy/paste the output and you'll get:
A clean breakdown of each step
Visual indicators for cost and rows
Callouts for slow or expensive parts of the plan
Get More Detail with Extended Options
For deep performance insight, run:
ANALYZE: executes the query and records actual behavior
COSTS: shows estimated execution costs
EXPLAIN (ANALYZE, COSTS, VERBOSE, BUFFERS, FORMAT JSON)
SELECT * FROM users WHERE email = '<EMAIL>';
VERBOSE: includes more details about plan internals
BUFFERS: shows memory/disk usage during execution
FORMAT JSON: useful for pasting into visual tools like Dalibo
You don't need to use all of these every time - but once you're debugging something tricky, they're
invaluable. If you're curious about query plans, I recommend reading more about them.
Adding Indexes Safely in Production
Adding an index can speed up queries - but if you do it the wrong way, it can lock tables, block
writes, and bring production to a crawl.
This is one of those operations that seems harmless until it takes your app down.
The Problem: Locks and Downtime
By default, when you run:
PostgreSQL acquires a lock that blocks all writes to the orders table until the index is fully
built. On large tables, that could be minutes (or hours) of downtime - even if the index is eventually
helpful.
The Safer Way: Use CONCURRENTLY
To avoid blocking reads or writes, always use CONCURRENTLY in production:
This tells Postgres to build the index in the background without locking the table for inserts,
updates, or deletes.
Warning
CREATE INDEX ON orders (user_id);
CREATE INDEX CONCURRENTLY CONCURRENTLY_orders_user_id ON orders (user_id);
CREATE INDEX CONCURRENTLY can't run inside a transaction block, and most
frameworks (like Rails or Ecto) wrap migrations in one by default. To safely create an index
concurrently, you'll need to:
Disable the transaction (disable_ddl_transaction! in Rails)
Use the appropriate syntax (algorithm: :concurrently in Rails)
Safely Replacing Indexes
If you're replacing an old index:
1. Create the new one concurrently
2. Update your app to use it (if needed)
3. Drop the old one concurrently
This prevents any locking at every step - and lets you roll forward or back safely.
Best Practices for Real-World Deployments
Adding indexes in production isn't just about syntax - it's about timing, safety, and minimizing risk.
These best practices go beyond CREATE INDEX CONCURRENTLY to help you avoid downtime,
reduce deploy friction, and make your indexing workflow smoother across environments.
Keep Concurrent Indexes Out of Migration Pipelines
Don't block your deploy pipeline waiting for a large index to build.
Instead: Merge a no-op placeholder migration (for historical clarity), and run the actual CREATE
INDEX CONCURRENTLY manually or in a separate, async deploy step.
Example:
CREATE INDEX CONCURRENTLY idx_new ON orders (user_id, created_at);
DROP INDEX CONCURRENTLY idx_old;
Monitor While Building
Even with CONCURRENTLY, index creation can slow down or stall. Large tables take time to scan.
High write volume means Postgres has to track and replay changes during a second "catch-up"
phase. And if disk I/O is constrained or your table is under heavy contention, the build can drag or
fail.
Monitoring progress with pg_stat_progress_create_index helps you catch these issues
early and avoid surprises.
This view shows how far along the build is, which phase it's in, and whether it's stalling.
Avoid Concurrent Indexes in High-Churn Tables During Peak Load
Concurrent index creation avoids locking writes, but still reads the whole table. On a massive,
frequently updated table, it can:
Create I/O pressure
class AddIndexOnOrdersUserId < ActiveRecord!"Migration[8.0]
disable_ddl_transaction!
def up
# No-op: Index will be created manually
execute !!'SQL
CREATE INDEX CONCURRENTLY IF NOT EXISTS index_orders_on_user_id ON
orders (user_id);
SQL
end
def down
execute !!'SQL
DROP INDEX CONCURRENTLY IF EXISTS index_orders_on_user_id;
SQL
end
end
SELECT * FROM pg_stat_progress_create_index;
Cause contention on hot pages
Take a long time
If possible, schedule index creation for off-peak hours or ensure replicas are healthy.
Tools That Help
You don't have to do everything manually - there are tools that help enforce best practices and
reduce risk when working with indexes in production. Some are built into your stack, others are part
of the broader Postgres ecosystem.
Strong Migrations (Ruby/Rails)
Warns you if you're about to add an index without CONCURRENTLY.
Manual Playbooks
For environments without migration tooling, consider writing a safe deploy checklist:
 Create index concurrently
 Monitor query performance and logs
 Drop old index once safe
pg_stat_statements
Tracks which queries are slow and run most frequently - perfect for deciding where to add
indexes.
pg_stat_progress_create_index
Shows how far along a background index build is and which phase it's in.
pgstattuple
Measures index and table bloat so you can decide whether to reindex or clean up unused
indexes.
PGHero
A dashboard for Postgres performance: slow queries, missing indexes, unused indexes, and
more.
Wrapping Up
Indexes can supercharge your queries - or quietly drag down your entire system. The difference
comes down to how well you understand the tradeoffs, the query planner, and the tools at your
disposal.
In this chapter, you learned how to reason about when to add an index, how to verify it with
EXPLAIN, how to use more advanced options like composite and partial indexes, and how to add
them safely in production without taking your app down.
You don't need to memorize internals or become a DBA. But if you can spot a slow query, interpret a
plan, and make thoughtful indexing decisions - you're already ahead of the curve.
Chapter 3: Query Pathologies,
Detection and Remediation
Some queries are slow because they're doing too much. Others are slow because they're doing
something dumb. This chapter is about the latter.
A query pathology is a recurring structural issue in how your code constructs or executes SQL. It
might not throw an error. It might even pass code review. But it slowly eats away at your system's
performance and in many cases, no one notices until things start timing out.
This chapter focuses on the most common pathologies in modern web apps: N+1 queries,
unbounded selects, over-fetching columns, join explosions, and more. You'll learn how to spot them
in logs and explain plans, and more importantly, how to fix or prevent them without rewriting your
entire app.
Each section includes real-world symptoms, root causes, and practical remediation strategies. If
you've ever stared at your query logs wondering how did we end up querying the same table 500 times
in one request?, you're in the right place.
N+1 Queries
One of the best parts about using an ORM is the ease of use. Unfortunately, yielding a tool this sharp
can make it all too easy to cut yourself. Enter the N+1 query: harmless at first glance, ruthless at
scale.
What is an N+1 Query?
An N+1 query occurs when your application makes one query to fetch a list of records (the "1"), and
then issues an additional query for each item in that list (the "N"). Put another way, the term "N+1"
comes from running 1 query for the parent set, then N additional queries for each child.
At a small scale, you might never notice. But as the number of records grows, so does the number of
queries - linearly. And because each query involves a round trip to the database, this pattern can
crush performance in production while remaining invisible in development or test environments.
An Example
Let's say you have an Author model and a Book model, and you're rendering a list of authors with
their books:
This will issue:
1. One query to load all authors.
2. Then one query per author to load their books.
Behind the scenes:
# Rails / ActiveRecord
Author.all.each do |author|
puts "!(author.name} wrote !(author.books.count} books"
end
!) 1 query for authors
SELECT * FROM authors;
!) 1 query per author to load books
SELECT COUNT(*) FROM books WHERE author_id = ?;
SELECT COUNT(*) FROM books WHERE author_id = ?;
!) !!$
So if you have 100 authors, your application just made 101 database calls. That's an N+1.
Other environments are no better:
Same idea, same problem. The pattern may be dressed differently, but the damage is the same:
chatty, repetitive queries that scale badly.
Why ORMs Make N+1s So Easy to Miss
N+1 queries slip through because the code looks fine. It reads cleanly, passes tests, and does exactly
what you asked-but not always what you meant.
Most ORMs default to lazy loading: they fetch related data only when you access it. Loop through a
list and reference an association, and the ORM quietly issues a new query each time. That's not a
bug, it's the default behavior.
This pattern exists across ecosystems: ActiveRecord, Ecto, Django, Sequelize. It's great for developer
ergonomics, but dangerous at scale. Dev and test environments are too small and fast to reveal the
problem. In production, it turns into a thundering herd of tiny queries.
Unless you're scanning logs or watching query volume, it's easy to miss until a list page in
production takes five seconds to load. That's why recognizing these patterns early is critical to
keeping your system fast and predictable.
How to Detect N+1s (Logs, Tools, Habits)
This section covers practical ways to spot N+1 queries before they cause problems - from reading
logs and using tooling to recognizing high-risk code patterns and common hotspots like serializers
and GraphQL resolvers.
Watch Query Volume in Your Logs
# Phoenix / Ecto
Repo.all(Author)
!* Enum.map(fn author !&
 "!(author.name} wrote !(length(Repo.preload(author, :books).books)} books"
end)
The first and most universal detection strategy is just… reading your logs. Nearly every ORM logs
queries by default in development, and any request that triggers a suspicious number of queries
should raise a red flag.
Consider this innocent-looking list view:
This will execute one query for authors, then one per author to fetch their books.
In your logs, it might look like:
If you see a pattern like:
1 query for the parent
N queries for the child
...you've got yourself an N+1.
Use Tools That Surface It Automatically
Some frameworks have tooling that will yell at you the moment you forget to eager-load an
association. As an example, there's the bullet gem for Rails applications:
Rails: Bullet
!!!+ Rails view !!,
<% @authors.each do |author| %>
<h2><%= author.name %>!-h2>
<ul>
 <% author.books.each do |book| %>
<li><%= book.title %> (<%= book.published_year %>)!-li>
 <% end %>
!-ul>
<% end %>
Author Load (1.2ms) SELECT * FROM authors
Book Load (0.6ms) SELECT * FROM books WHERE author_id = 1
Book Load (0.5ms) SELECT * FROM books WHERE author_id = 2
Book Load (0.6ms) SELECT * FROM books WHERE author_id = 3
!!$
Then you might see the following in your logs:
Bullet will also give you a stack trace pointing to the offending line of code. Pretty convenient!
APM Services (Scout, Skylight, New Relic)
Most full-stack APMs now auto-detect N+1 patterns and surface "slow query stacks" in their
dashboards, no code changes required beyond installing the agent.
How to Prevent N+1s
What's better than getting rid of N+1 queries? Not having them in the first place, of course. There
are a few ways to go about this, depending on the API you're working on. Here are the most reliable
prevention strategies across different types of backends.
Eager Loading Best Practices
Eager loading means telling your ORM to fetch associated data up front, in the same database
query (or a small set of known queries), instead of waiting until you access it later. As mentioned,
most ORMs use lazy loading by default. That means if you call author.books inside a loop, the
books won't be fetched until that line runs, causing a new query for every author. Eager loading flips
that around. You load everything you need in one go, before entering the loop.
# Gemfile
gem 'bullet'
N+1 Query detected
Author !. [:books]
Add to your query: .includes(:books)
# Without eager loading - triggers N+1
authors = Author.all
authors.each { |a| puts a.books.count }
# With eager loading - avoids N+1
authors = Author.includes(:books).all
authors.each { |a| puts a.books.count }
The performance difference is huge: instead of making 101 queries to render 100 authors and their
book count, you'll make just 1 for authors + 1 for all books.
Note
There are different ways to eager load associated records and each way has its own nuance. We
won't dive into the specifics here as it will vary based on the ORM, but as an example, Rails
also gives you preload and eager_load. They all preload data differently depending on
context.
GraphQL Lookaheads
GraphQL encourages clients to request exactly the data they need, but that flexibility makes it
dangerously easy to trigger N+1s, especially when nesting fields. If your resolvers load associations
lazily, a single query like:
can explode into dozens or hundreds of individual SQL queries.
That's where lookaheads come in.
Lookahead features let your resolvers peek at the full selection set before executing any queries. That
means you can conditionally eager-load just the associations the client actually requested.
{
 posts {
 title
 comments {
 author {
 name
 }
 }
 }
}
Now whether the client asks for just posts or deeply nested comment authors, you fetch everything
efficiently in one go.
When to Use Lookahead
You're returning nested associations (post.comments.author)
You want to avoid blindly eager-loading everything
You're seeing N+1 patterns even after trying includes/preloads
You want resolvers that adapt to the shape of the query without extra round-trips
Batch Patterns
Sometimes you can't eager load everything ahead of time. Maybe the data is conditional, deeply
nested, or only accessed after the main query has run. In those cases, eager loading can't save you,
but batch loading can.
Batch loading is a pattern that groups multiple similar queries together into one. Instead of firing a
separate query every time you load an association, you collect all those requests and resolve them in
bulk.
Note
def resolve
lookahead = context.lookahead
inclusions = []
if lookahead.selects?(:comments)
inclusions !/ :comments
if lookahead.selection(:comments).selects?(:author)
inclusions !/ { comments: :author }
end
end
Post.includes(*inclusions).all
end
Dataloader is a common implementation of this pattern, especially in GraphQL backends. It
batches all .load(id) calls during the same execution tick, fetches the data in a single query,
and caches the results for the duration of the request. That means you don't just reduce query
count, you eliminate redundant work too. I recommend looking it up if you're working in a
GraphQL API!
A Classic Example (GraphQL-Ruby)
Let's say your Comment type has a field for author, and you're returning many comments at once:
This runs one query per comment. With 100 comments, that's 100 queries.
Now with BatchLoader:
Now all author_id values get collected and loaded in a single query, one query total, no matter
how many comments.
Where Batch Loaders Shine
Associations accessed dynamically (e.g. within loops or nested fields)
Fields that aren't always requested, but need to be fast when they are
Avoiding duplication of includes logic across resolvers
Preventing N+1s in third-party code (serializers, libraries)
# Naive (N+1)
def author
User.find(object.author_id)
end
# Efficient
def author
BatchLoader!"GraphQL.for(object.author_id).batch do |author_ids, loader|
User.where(id: author_ids).each do |user|
loader.call(user.id, user)
end
end
end
Authors Books
Alice
Bob
Odyssey
War and Peace
1984
In practice, high-performance GraphQL APIs use both lookaheads and batch loading:
Eager load where possible
Batch load wherever lazy access is unavoidable
Together, they give you control over query shape-and help ensure your app doesn't fall into the N+1
trap every time it grows.
Unbounded SELECTs and Cartesian Joins
Sometimes the problem isn't how many queries you're running, it's how big they are.
An unbounded SELECT is a query that fetches everything from a table without constraints: no
WHERE, no LIMIT, no pagination, no mercy. Pair that with a careless JOIN, and you might
accidentally trigger a Cartesian product: every row in table A joined to every row in table B.
Congratulations, you've built a memory-killing monster.
An example of a Cartesian join:
2 Authors × 3 Books = 6 Rows
Symptoms: Massive Rows, Memory Spikes
These problems usually sneak in during development. On small datasets, everything feels fine. But
in production, your database is suddenly sending back hundreds of thousands or millions of rows.
Your app might OOM. The page might take 30 seconds to render. Or the query might quietly chew
up CPU for minutes, locking rows and starving other queries.
Key red flags:
Queries returning tens or hundreds of thousands of rows
Sudden spikes in memory usage or load on the DB
Out-of-memory crashes in your app or background jobs
Long-running queries with merge or nested loop joins in EXPLAIN
Even if the app survives, performance takes a hit. Response times degrade. Throughput plummets.
Detection: Log-Sampling, DB Metrics
These issues don't always show up in tests, so proactive detection matters.
Start with your slow query logs. Many ORMs will log queries by default; skim through those and
look for SELECT * patterns or suspicious joins. A query returning 10,000 rows is almost always a
smell.
Enable database-level metrics if possible. Tools like pg_stat_statements, DataDog APM, or New
Relic can show:
Queries with high rows returned per call
High total I/O or CPU usage per query
High memory or temp file usage (Postgres spills large joins to disk)
In some stacks, you can even track the number of rows returned per query in logs. Anything over a
few thousand rows deserves scrutiny.
Fix: Add Predicates, JOIN Filters, Explicit Projections
If you're seeing queries that return far too much data, you usually need to constrain the query.
Add WHERE clauses. Start with user scopes, time ranges, or relevant filters.
Add LIMIT or pagination. Never render an entire table in one request.
Be selective with JOINs. Always ensure you're joining on a key. If you forget the ON clause
entirely, you'll get a Cartesian join by default.
Project only what you need. Avoid SELECT * especially on wide tables or JOINs. Fetch
only the fields your app actually uses.
For example:
Unbounded queries often slip in quietly through admin dashboards, CSV exports, or eager JOINs in
a GraphQL resolver. Keep an eye on the shape and size of every query. The earlier you catch it, the
better your system will scale.
SELECT * & Over-Selecting Columns
By defaulting to SELECT *, or otherwise fetching more columns than your application actually
uses, you're paying a hidden tax on I/O, CPU, network, and memory. Over-selecting bloats result
sets, slows down query pipelines, and can turn an otherwise efficient read into a costly operation,
especially on wide tables or high‑throughput workloads.
Why grabbing every field kills throughput
When you ask the database to return every column, it has to:
!) Bad: unbounded SELECT with JOIN
SELECT * FROM users JOIN orders ON true;
!) Better: filter both sides and project specific columns
SELECT users.id, users.email, orders.total
FROM users
JOIN orders ON orders.user_id = users.id
WHERE users.created_at !0 now() - interval '30 days';
1. Read more bytes from disk: Wider rows mean fewer rows per page. Every table page fetch
retrieves fewer usable results, resulting in more page reads overall.
2. Consume more network bandwidth: Transferring unused columns over the wire increases
latency for both the database and your application.
3. Increase CPU and memory pressure: Deserializing large row buffers and materializing
unnecessary fields in your runtime takes CPU cycles and bloats your process heap.
Even if you only need one or two fields, a SELECT * forces the engine to load every attribute into
memory and hand it off to the client. In high‑concurrency environments, those extra bytes add up,
slowing down caches, compounding I/O costs, and exacerbating GC pauses.
Detection: explaining "row width" vs "heap fetches"
Most SQL engines include instrumentation to surface how much data you're pulling and how often
you're hitting the storage layer:
Row width: The average number of bytes per row returned by your query. In Postgres, you
can see this in EXPLAIN (ANALYZE, VERBOSE) under the Rows Removed by Filter
and Width metrics. A large "row width" compared to the size of the columns you actually use
is a red flag.
Heap fetches (or physical reads): The count of times the planner needed to go out to the
table's heap pages to satisfy your query. Over‑selecting increases heap fetches per meaningful
row, since each page contains fewer projected rows when you're pulling every column.
By comparing a narrow projection (explicit column list) versus SELECT * in your EXPLAIN output,
you'll often see: fewer I/O operations, smaller row widths, and reduced query runtime when you
limit your projection.
Remediation: ActiveRecord Patterns
Below are three core remediation strategies, illustrated with ActiveRecord examples:
1. Explicit column lists: Always list only the columns you need. This trims data at the source.
2. DTOs and projection layers: Use lightweight objects or raw attribute arrays to avoid
hydrating full ActiveRecord instances.
3. Specialized or materialized views: For complex joins or frequent reports, define database
views and map them via ActiveRecord models (or use materialized views).
# Instead of fetching full models:
orders = Order.where(status: 'pending')
# !. SELECT * FROM orders WHERE status = 'pending'
# Do:
orders = Order.where(status: 'pending').select(:id, :user_id,
:created_at)
# !. SELECT id, user_id, created_at FROM orders WHERE status = 'pending'
# Bad: loading entire records
users = User.where(active: true).to_a
# Good: fetch lightweight structs
UserDTO = Struct.new(:id, :email)
records = User.where(active: true)
.pluck(:id, :email)
.map { |id,email| UserDTO.new(id, email) }
!) On the DB side:
CREATE MATERIALIZED VIEW monthly_sales AS
SELECT date_trunc('month', created_at) AS month,
SUM(total) AS revenue
FROM orders
GROUP BY month;
By applying these patterns: explicit selects, targeted projections, and specialized views, you'll prevent
unbounded SELECTs and Cartesian join fallout at the ActiveRecord layer.
Ghost Queries from Plugins/Serializers
Plugins, serializers, or model callbacks can quietly issue extra SQL queries behind the scenes, often
outside the main application code, leading to unexpected performance regressions. These hidden
"ghost queries" can go unnoticed during development but wreak havoc under load.
How serializers or callbacks sneak in extra queries
Many frameworks and ORMs allow plugins or serializers to define callbacks that fire during object
hydration or rendering and in Rails/ActiveRecord, these can issue hidden SQL per record. Common
ActiveRecord patterns that introduce ghost queries include:
Association touch-ups in serializers
# In Rails:
class MonthlySale < ApplicationRecord
self.table_name = 'monthly_sales'
# optional: disable write behavior
def readonly?; true; end
end
# Usage:
MonthlySale.where(month: Date.current.beginning_of_month)
class UserSerializer < ActiveModel!"Serializer
attributes :id, :name, :email
has_one :profile
def profile
object.profile # issues a query per user if not eager-loaded
end
end
Rendering a collection with User.all will fire one SELECT for users, then one SELECT per user
for profiles unless you eager-load.
Computed attributes with callbacks or methods
Fetching orders (e.g., Order.where(status: 'pending')) triggers an extra query per order to
look up its discount name.
Callback chains in models
Every instantiation or fetch of Product records fires AnalyticsEvent.create!, causing
unexpected writes at render time.
These patterns often slip past notice in development but multiply under load, leading to N+1–style
bloat hidden within serializers or model callbacks.
Detection: SQL Tracing and Request Profiling
To root out ghost queries, use these techniques:
SQL tracing: Capture every SQL statement executed during a request.
Here's an example using ActiveSupport!"Notifications in Rails:
class Order < ApplicationRecord
after_find :load_discount_name
def load_discount_name
self.discount_name = Discount.find(discount_id).name
end
end
class Product < ApplicationRecord
after_initialize :track_initial_load
def track_initial_load
AnalyticsEvent.create!(product_id: id, event: 'initialized')
end
end
Alternatively, use Postgres's pg_stat_statements extension to aggregate query metrics at the
database level.
Request profiling: Use profiling tools to surface N+1s and total query counts per request or view.
Bullet gem (Rails): Notifies you immediately of N+1 queries and unused eager-loads during
development.
rack-mini-profiler: Displays an in-page performance panel with SQL query counts and
execution timings per request.
Log analysis: Aggregate slow-query logs and inspect unexpected queries originating from
serializers or callbacks by correlating timestamps and stack traces.
By correlating record counts with SQL statements (for instance, rendering 10 users but seeing 20+
queries), you'll pinpoint which serializers or callbacks are generating ghost queries.
Fix: memoize, move logic into joins, extract to background
Once you identify the offending ghost queries, you can remediate them:
1. Memoization: Cache expensive lookups within the scope of a request or serializer instance to
prevent duplicate queries. For example, in an ActiveModel serializer:
ActiveSupport!"Notifications.subscribe('sql.active_record') do |_, start,
finish, _, payload|
next if payload[:name] !1 'SCHEMA' # skip internal queries
duration = ((finish - start) * 1000).round(1)
Rails.logger.debug("[SQL TRACE] !(payload[:sql]} (!(duration}ms)")
end
Here, each unique discount_id is fetched only once per request.
2. Join-based projections: Pull related data in the original query using JOIN and select, so
all needed fields are loaded together in one round‑trip:
This avoids per-record callbacks by letting SQL return discount_name alongside each order.
3. Background extraction: Offload costly or external lookups to background jobs, and serve
pre-computed results via cache or a dedicated column. For example:
class OrderSerializer < ActiveModel!"Serializer
attributes :id, :total, :discount_name
def discount_name
@discounts !!2 {} # memoization cache
@discounts[object.discount_id] !!2
Discount.find(object.discount_id).name
end
end
orders = Order
.joins(:discount)
.select(
'orders.id, orders.total, ' \
'discounts.name AS discount_name'
 )
.where(status: 'pending')
# app/jobs/fetch_pricing_job.rb
class FetchPricingJob < ApplicationJob
def perform(order_id)
order = Order.find(order_id)
price = ExternalApi.get_price(order.product_code)
order.update_column(:cached_price, price)
end
end
# In controller
order = Order.find(params[:id])
Users get a fast response with the last known cached_price, and heavy API calls run
asynchronously. Applying these fixes will keep your rendering layer lean and predictable,
ensuring that serializers and callbacks don't become silent performance traps.
Pagination Performance Cliffs
The most dangerous performance problems are the ones that work perfectly in development.
Pagination is the poster child for this category: your local dataset of 100 records pages beautifully,
while your production database with 10 million records is slowly dying as users browse deep into
your result sets.
What Goes Wrong
OFFSET/LIMIT pagination looks deceptively simple and works exactly as you'd expect until you
start dealing with many records:
The Hidden Cost of OFFSET
Here's what your database actually does with OFFSET 10000:
1. Builds the full result set from the beginning
FetchPricingJob.perform_later(order.id)
render json: {
id: order.id,
price: order.cached_price !3 'pending'
}
!) Page 1: Lightning fast
SELECT * FROM products ORDER BY created_at DESC LIMIT 20 OFFSET 0;
!) Execution time: 2ms
!) Page 500: Database destruction
SELECT * FROM products ORDER BY created_at DESC LIMIT 20 OFFSET 10000;
!) Execution time: 800ms
2. Sorts all those rows according to your ORDER BY
3. Counts and discards the first 10,000 rows
4. Finally returns rows 10,001-10,020
You're forcing the database to do 99.8% wasted work to return 0.2% of the results.
Think of it like reading a book by starting from page 1 every single time, counting pages until you
reach page 500. It doesn't matter that you have a bookmark, OFFSET pagination throws it away and
starts counting from the beginning.
Detection: Finding Your Problem Pages
The tricky part about pagination performance is that it's not obviously broken, it just gets
progressively worse. Here's how to catch it before users complain:
But raw SQL doesn't tell you the user impact. Add application-level tracking:
!) PostgreSQL: Find your worst offenders
SELECT
query,
 calls,
 mean_exec_time,
 max_exec_time,
(mean_exec_time * calls)!"bigint as total_time_ms
FROM pg_stat_statements
WHERE query LIKE '%OFFSET%'
AND query NOT LIKE '%pg_stat_statements%' !) Exclude this query
AND mean_exec_time > 100 !) Over 100ms average
ORDER BY total_time_ms DESC
LIMIT 20;
# Simple middleware to track pagination depth
class PaginationMonitor
def initialize(app)
@app = app
end
Solution 1: Keyset Pagination (The Right Way™)
Instead of counting rows to skip, keyset pagination uses the actual values from your last result as a
starting point. It's like using a bookmark instead of counting pages from the beginning.
How it works:
Instead of "give me page 5", you say "give me records after ID 12345"
The database can jump directly to that ID using an index
Performance is O(log N) regardless of how deep you go
def call(env)
start = Time.now
status, headers, response = @app.call(env)
if env['QUERY_STRING'].include?('page=')
page = Rack!"Utils.parse_query(env['QUERY_STRING'])['page'].to_i
duration = Time.now - start
if page > 50 !3 duration > 1.0
Rails.logger.warn "[PAGINATION] Deep page request",
page: page,
duration: duration,
path: env['PATH_INFO']
end
end
 [status, headers, response]
end
end
# Traditional offset approach - gets slower with each page
def page_one
Product.order(created_at: :desc).limit(20).offset(0) # Fast
end
The clever bit: We use (created_at, id) as a composite key. This handles the case where
multiple records have the same timestamp - the ID breaks the tie, ensuring stable ordering.
When to use it:
Infinite scroll interfaces
API pagination where you control the client
Any scenario where you don't need to jump to arbitrary pages
The downside:
No jumping to page 47 directly
URLs aren't shareable (no page numbers)
Users can't see "page 3 of 97"
Solution 2: Cursor-Based Pagination (Keyset in Disguise)
Cursor pagination is keyset pagination with the implementation details hidden behind an opaque
token.
def page_fifty
Product.order(created_at: :desc).limit(20).offset(1000) # Slow - reads
1000 rows first
end
# Keyset approach - equally fast at any depth
def first_page
Product.order(created_at: :desc).limit(20)
end
def next_page(last_created_at, last_id)
Product
.where("(created_at, id) < (?, ?)", last_created_at, last_id)
.order(created_at: :desc, id: :desc)
.limit(20)
end
Why bother with encoding?
API stability: You can change the underlying implementation without breaking clients
Security: Users can't guess or manipulate cursor values
Flexibility: Cursors can encode multiple sort fields, filters, or even query versions
Real-world example: GitHub's GraphQL API uses cursor pagination exclusively. Their cursors are
base64-encoded tokens that hide the underlying implementation details, allowing GitHub to change
how pagination works without breaking client applications.
Solution 3: The Hybrid Approach (Pragmatic Reality)
Sometimes you need page numbers for UX but want to avoid the worst performance cliffs. The
hybrid approach sets boundaries:
But wait, this still uses OFFSET!
def encode_cursor(last_record)
Base64.encode64("!(last_record.created_at.to_i}:!(last_record.id}")
end
def decode_cursor(cursor)
timestamp, id = Base64.decode64(cursor).split(':')
 [Time.at(timestamp.to_i), id.to_i]
end
class Product < ApplicationRecord
MAX_PAGE = 100
def self.paginate(page: 1, per: 20)
raise "Page !(page} exceeds maximum of !(MAX_PAGE}" if page > MAX_PAGE
# Use OFFSET for allowed range
offset((page - 1) * per).limit(per)
end
end
Yes, but with crucial differences:
By capping at page 100, your worst-case query only skips 2,000 rows, not 2 million
You can pre-warm caches for common pages (1-10)
Deep pagination becomes a search problem, not a browsing problem
Making it user-friendly:
Solution 4: Optimizing OFFSET When You're Stuck With It
Sometimes you can't change the pagination method due to legacy code, API contracts, or stubborn
stakeholders. Here's how to make OFFSET less painful:
1. Use covering indexes:
2. The seek method (OFFSET hybrid):
def index
if params[:page].to_i > 50
# Encourage search instead of browsing
flash.now[:notice] = "Looking for something specific? Try our search!"
end
@products = Product.paginate(page: params[:page])
rescue Product!"PaginationError
redirect_to search_products_path,
alert: "Deep pages aren't available. Please use search to find specific
items."
end
!) Instead of reading the full table rows
CREATE INDEX idx_products_pagination
ON products(created_at DESC, id DESC)
INCLUDE (name, price, status);
!) The database can now satisfy common queries entirely from the index
!) without touching the actual table data
This approach runs two queries but the subquery only fetches one column from the index, making it
much faster than fetching all columns for 10,000 rows.
3. Cache common pages aggressively:
When to Use Each Approach
Use Keyset/Cursor when:
!) Instead of: SELECT * FROM products ORDER BY created_at DESC LIMIT 20
OFFSET 10000;
!) Do this:
SELECT * FROM products
WHERE created_at !4 (
SELECT created_at FROM products
ORDER BY created_at DESC
LIMIT 1 OFFSET 10000
)
ORDER BY created_at DESC
LIMIT 20;
class ProductsController < ApplicationController
def index
page = params[:page].to_i
if page !4 10 # Cache the first 10 pages
@products = Rails.cache.fetch("products/page/!(page}", expires_in:
5.minutes) do
Product.page(page).to_a # to_a forces evaluation
end
else
@products = Product.page(page)
end
end
end
Building APIs (especially mobile)
Implementing infinite scroll
Data changes frequently (timeline feeds)
Deep pagination is common
Use Hybrid (OFFSET with limits) when:
You need page numbers for UX
SEO requires crawlable pages
Users expect traditional pagination
Most usage is shallow (pages 1-5)
Use optimized OFFSET when:
You literally cannot change the implementation
Breaking API compatibility isn't an option
The cost of refactoring exceeds the performance gain
The Nuclear Option: Don't Paginate
The best pagination is often no pagination. Before optimizing pagination, consider:
1. Better search/filtering - If users go past page 3, your search has failed
2. Load more patterns - Show 20 items with a "Load 20 more" button
3. Virtual scrolling - For truly large datasets, render only visible items
4. Alphabetical/date indexes - "Jump to April 2024" beats "Page 847"
Key Takeaways
1. OFFSET pagination has O(n) complexity - This is math, not opinion. Page 1000 will always
be ~1000× slower than page 1.
2. Monitor pagination depth - You can't fix what you don't measure. Track how deep users
actually go.
3. Set hard limits - If someone wants page 500, they're either a bot or need better search tools.
4. Choose based on use case - Keyset for APIs, hybrid for web UIs, optimized OFFSET when
stuck.
5. Question the need - Deep pagination is often a symptom of missing features, not a
requirement.
Remember: every major platform has moved away from deep pagination. Twitter uses cursors.
Reddit uses "load more". Instagram has infinite scroll. They didn't do this to be trendy, they did it
because OFFSET pagination doesn't scale.