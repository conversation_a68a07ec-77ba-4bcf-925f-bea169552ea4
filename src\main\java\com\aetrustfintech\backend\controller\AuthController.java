package com.aetrustfintech.backend.controller;

import com.aetrustfintech.backend.dto.auth.*;
import com.aetrustfintech.backend.service.auth.AuthService;
import com.aetrustfintech.backend.service.auth.OtpService;
import com.aetrustfintech.backend.service.user.UserRegistrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "Authentication", description = "Authentication and authorization endpoints")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    private final AuthService authService;
    private final OtpService otpService;

    @Autowired
    public AuthController(AuthService authService, OtpService otpService) {
        this.authService = authService;
        this.otpService = otpService;
    }

    @Operation(
        summary = "Register a new user",
        description = "Creates a new user account and sends verification OTPs to email and phone"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "User registered successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = AuthResponse.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input data or user already exists"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    @PostMapping("/register")
    public ResponseEntity<AuthResponse> register(@Valid @RequestBody RegisterRequest request) {
        logger.info("Registration request received for email: {}", request.getEmail());
        
        AuthResponse response = authService.register(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @Operation(
        summary = "User login",
        description = "Authenticates user with email/phone/username and password"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Login successful",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = AuthResponse.class))
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Invalid credentials or account locked"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input data"
        )
    })
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest request, HttpServletRequest httpRequest) {
        logger.info("Login request received for identifier: {}", request.getIdentifier());
        
        // Set device info and IP address
        request.setDeviceInfo(getDeviceInfo(httpRequest));
        request.setIpAddress(getClientIpAddress(httpRequest));
        
        AuthResponse response = authService.login(request);
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Verify OTP",
        description = "Verifies OTP code for registration, login, or other purposes"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "OTP verified successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid or expired OTP"
        )
    })
    @PostMapping("/verify-otp")
    public ResponseEntity<AuthResponse> verifyOtp(@Valid @RequestBody OtpRequest request) {
        logger.info("OTP verification request for identifier: {}", request.getIdentifier());
        
        boolean verified = otpService.verifyOtp(request);
        
        if (verified) {
            return ResponseEntity.ok(AuthResponse.success("OTP verified successfully", null, null, null, null));
        } else {
            return ResponseEntity.badRequest().body(AuthResponse.error("Invalid or expired OTP"));
        }
    }

    @Operation(
        summary = "Resend OTP",
        description = "Resends OTP to the specified identifier"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "OTP sent successfully"
        ),
        @ApiResponse(
            responseCode = "429",
            description = "Too many requests"
        )
    })
    @PostMapping("/resend-otp")
    public ResponseEntity<AuthResponse> resendOtp(@RequestParam String identifier, @RequestParam String otpType) {
        logger.info("Resend OTP request for identifier: {} and type: {}", identifier, otpType);
        
        otpService.sendOtp(identifier, otpType);
        
        return ResponseEntity.ok(AuthResponse.success("OTP sent successfully", null, null, null, null));
    }

    @Operation(
        summary = "Refresh access token",
        description = "Generates a new access token using refresh token"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Token refreshed successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = AuthResponse.class))
        ),
        @ApiResponse(
            responseCode = "401",
            description = "Invalid refresh token"
        )
    })
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@RequestParam String refreshToken) {
        logger.info("Token refresh request received");
        
        AuthResponse response = authService.refreshToken(refreshToken);
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Logout user",
        description = "Logs out user and invalidates refresh token"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Logout successful"
        )
    })
    @PostMapping("/logout")
    public ResponseEntity<AuthResponse> logout(@RequestParam String refreshToken) {
        logger.info("Logout request received");
        
        authService.logout(refreshToken);
        
        return ResponseEntity.ok(AuthResponse.success("Logout successful", null, null, null, null));
    }

    @Operation(
        summary = "Logout from all devices",
        description = "Logs out user from all devices by invalidating all refresh tokens"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Logout from all devices successful"
        )
    })
    
    @PostMapping("/logout-all")
    public ResponseEntity<AuthResponse> logoutAll(@RequestParam String userId) {
        logger.info("Logout all devices request for user ID: {}", userId);

        UUID userUuid;
        try {
            userUuid = UUID.fromString(userId);
        } catch (IllegalArgumentException e) {
            try {
                Long userIdLong = Long.parseLong(userId);
                // For backward compatibility, we'll need to look up the user by the old ID
                // For now, throw an error since we can't convert Long to UUID without database lookup
                return ResponseEntity.badRequest().body(
                    AuthResponse.error("Invalid user ID format. Please use UUID format.")
                );
            } catch (NumberFormatException ex) {
                return ResponseEntity.badRequest().body(
                    AuthResponse.error("Invalid user ID format")
                );
            }
        }

        authService.logoutAllDevices(userUuid);
        
        return ResponseEntity.ok(AuthResponse.success("Logged out from all devices", null, null, null, null));
    }

    private String getDeviceInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "Unknown Device";
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
