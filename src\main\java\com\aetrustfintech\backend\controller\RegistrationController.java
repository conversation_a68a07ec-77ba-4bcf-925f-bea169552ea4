package com.aetrustfintech.backend.controller;

import com.aetrustfintech.backend.dto.auth.*;
import com.aetrustfintech.backend.service.user.UserRegistrationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/registration")
@Tag(name = "User Registration", description = "Multi-step user registration process")
public class RegistrationController {

    private static final Logger logger = LoggerFactory.getLogger(RegistrationController.class);

    private final UserRegistrationService registrationService;

    @Autowired
    public RegistrationController(UserRegistrationService registrationService) {
        this.registrationService = registrationService;
    }

    @Operation(
        summary = "Initiate user registration",
        description = "Start the multi-step registration process with basic user information"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "201",
            description = "Registration initiated successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = RegistrationStatusResponse.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input data or user already exists"
        ),
        @ApiResponse(
            responseCode = "429",
            description = "Too many registration attempts"
        )
    })
    @PostMapping("/initiate")
    public ResponseEntity<Map<String, Object>> initiateRegistration(
            @Valid @RequestBody InitialRegistrationRequest request,
            HttpServletRequest httpRequest) {
        
        logger.info("Registration initiation request received for email: {}", request.getEmail());

        // Set device and security info
        request.setRegistrationIpAddress(getClientIpAddress(httpRequest));
        request.setDeviceInfo(getDeviceInfo(httpRequest));
        request.setUserAgent(httpRequest.getHeader("User-Agent"));

        RegistrationStatusResponse response = registrationService.initiateRegistration(request);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Registration initiated successfully. Please verify your phone number.");
        result.put("data", response);

        return ResponseEntity.status(HttpStatus.CREATED).body(result);
    }

    @Operation(
        summary = "Verify phone number",
        description = "Verify phone number with OTP code sent via SMS"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Phone number verified successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid OTP code or registration not found"
        )
    })
    @PostMapping("/{registrationId}/verify-phone")
    public ResponseEntity<Map<String, Object>> verifyPhoneNumber(
            @PathVariable Long registrationId,
            @RequestParam String otpCode) {
        
        logger.info("Phone verification request for registration ID: {}", registrationId);

        RegistrationStatusResponse response = registrationService.verifyPhoneNumber(registrationId, otpCode);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Phone number verified successfully. Please verify your email address.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Verify email address",
        description = "Verify email address with OTP code sent via email"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Email address verified successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid OTP code or registration not found"
        )
    })
    @PostMapping("/{registrationId}/verify-email")
    public ResponseEntity<Map<String, Object>> verifyEmail(
            @PathVariable Long registrationId,
            @RequestParam String otpCode) {
        
        logger.info("Email verification request for registration ID: {}", registrationId);

        RegistrationStatusResponse response = registrationService.verifyEmail(registrationId, otpCode);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Email address verified successfully. Please upload your ID document.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Submit identity verification",
        description = "Upload ID document and personal information for identity verification"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Identity verification submitted successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid ID format or registration not found"
        )
    })
    @PostMapping("/identity-verification")
    public ResponseEntity<Map<String, Object>> submitIdentityVerification(
            @Valid @RequestBody IdentityVerificationRequest request) {
        
        logger.info("Identity verification request for registration ID: {}", request.getRegistrationId());

        RegistrationStatusResponse response = registrationService.submitIdentityVerification(request);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Identity verification submitted successfully. Please set your transaction PIN.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Set transaction PIN",
        description = "Set a secure transaction PIN for account security"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Transaction PIN set successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid PIN or registration not found"
        )
    })
    @PostMapping("/transaction-pin")
    public ResponseEntity<Map<String, Object>> setTransactionPin(
            @Valid @RequestBody TransactionPinRequest request) {
        
        logger.info("Transaction PIN setup request for registration ID: {}", request.getRegistrationId());

        RegistrationStatusResponse response = registrationService.setTransactionPin(request);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Transaction PIN set successfully.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Set biometric status",
        description = "Enable or disable biometric authentication"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Biometric status updated successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request or registration not found"
        )
    })
    @PostMapping("/biometric-status")
    public ResponseEntity<Map<String, Object>> setBiometricStatus(
            @Valid @RequestBody BiometricStatusRequest request) {
        
        logger.info("Biometric status request for registration ID: {}", request.getRegistrationId());

        RegistrationStatusResponse response = registrationService.setBiometricStatus(request);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Biometric status updated successfully.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Complete registration",
        description = "Finalize the registration process and create the user account"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Registration completed successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Registration not ready for completion"
        )
    })
    @PostMapping("/{registrationId}/complete")
    public ResponseEntity<Map<String, Object>> completeRegistration(@PathVariable Long registrationId) {
        
        logger.info("Registration completion request for ID: {}", registrationId);

        RegistrationStatusResponse response = registrationService.completeRegistration(registrationId);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Registration completed successfully! Welcome to AeTrust Fintech.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Get registration status",
        description = "Get the current status and progress of a registration"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Registration status retrieved successfully"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Registration not found"
        )
    })
    @GetMapping("/{registrationId}/status")
    public ResponseEntity<Map<String, Object>> getRegistrationStatus(@PathVariable Long registrationId) {
        
        logger.info("Registration status request for ID: {}", registrationId);

        RegistrationStatusResponse response = registrationService.getRegistrationStatus(registrationId);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", "Registration status retrieved successfully.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Resend verification code",
        description = "Resend OTP code for phone or email verification"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Verification code sent successfully"
        ),
        @ApiResponse(
            responseCode = "429",
            description = "Too many requests"
        )
    })
    @PostMapping("/{registrationId}/resend-code")
    public ResponseEntity<Map<String, Object>> resendVerificationCode(
            @PathVariable Long registrationId,
            @RequestParam String type) { // "phone" or "email"

        logger.info("Resend verification code request for registration ID: {} and type: {}", registrationId, type);

        RegistrationStatusResponse registration = registrationService.getRegistrationStatus(registrationId);

        // Use the new resend verification method
        RegistrationStatusResponse response = registrationService.resendVerification(
            registration.getEmail(),
            registration.getPhone(),
            type
        );

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", String.format("Verification code sent successfully to your %s.", type));
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Check registration status by email/phone",
        description = "Check if a user has an existing registration and get guidance on next steps"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Registration status retrieved successfully"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "No registration found"
        )
    })
    @PostMapping("/check-status")
    public ResponseEntity<Map<String, Object>> checkRegistrationStatus(
            @RequestParam String email,
            @RequestParam String phone) {

        logger.info("Registration status check for email: {}", email);

        RegistrationStatusResponse response = registrationService.getRegistrationStatus(email, phone);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", response.getMessage() != null ? response.getMessage() : "Registration status retrieved successfully.");
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    @Operation(
        summary = "Resend verification by email/phone",
        description = "Resend verification code for users who don't have registration ID"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Verification code sent successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid request or verification already completed"
        )
    })
    @PostMapping("/resend-verification")
    public ResponseEntity<Map<String, Object>> resendVerificationByContact(
            @RequestParam String email,
            @RequestParam String phone,
            @RequestParam String type) { // "phone" or "email"

        logger.info("Resend verification request for email: {} and type: {}", email, type);

        RegistrationStatusResponse response = registrationService.resendVerification(email, phone, type);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("success", true);
        result.put("message", String.format("Verification code sent successfully to your %s.", type));
        result.put("data", response);

        return ResponseEntity.ok(result);
    }

    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    private String getDeviceInfo(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent != null ? userAgent : "Unknown Device";
    }
}
