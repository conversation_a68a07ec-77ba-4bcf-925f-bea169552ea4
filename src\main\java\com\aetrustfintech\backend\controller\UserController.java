package com.aetrustfintech.backend.controller;

import com.aetrustfintech.backend.dto.auth.UpdatePasswordRequest;
import com.aetrustfintech.backend.dto.user.UserResponse;
import com.aetrustfintech.backend.service.user.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/users")
@SecurityRequirement(name = "bearerAuth")
@Tag(name = "User Management", description = "User profile and account management endpoints")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    private final UserService userService;

    @Autowired
    public UserController(UserService userService) {
        this.userService = userService;
    }

    @Operation(
        summary = "Get current user profile",
        description = "Returns the profile information of the currently authenticated user"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "User profile retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserResponse.class))
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User not authenticated"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found"
        )
    })
    @GetMapping("/me")
    public ResponseEntity<Map<String, Object>> getCurrentUser(@RequestAttribute("userId") UUID userId) {
        logger.info("Getting current user profile for user ID: {}", userId);

        UserResponse user = userService.getUserById(userId);
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "User profile retrieved successfully");
        response.put("data", user);
        
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get user by ID",
        description = "Returns user information by user ID (Admin only)"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "User retrieved successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserResponse.class))
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "User not found"
        )
    })
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> getUserById(@PathVariable String id) {
        logger.info("Getting user by ID: {}", id);

        // Support both UUID and Long formats for backward compatibility
        UUID userId;
        try {
            userId = UUID.fromString(id);
        } catch (IllegalArgumentException e) {
            // If not a valid UUID, try to parse as Long and convert
            try {
                Long longId = Long.parseLong(id);
                // For backward compatibility, we'll need to handle this case
                throw new IllegalArgumentException("Long ID format no longer supported. Please use UUID format.");
            } catch (NumberFormatException ex) {
                throw new IllegalArgumentException("Invalid ID format. Must be a valid UUID.");
            }
        }

        UserResponse user = userService.getUserById(userId);
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "User retrieved successfully");
        response.put("data", user);
        
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get all users",
        description = "Returns paginated list of all users (Admin only)"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Users retrieved successfully"
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied"
        )
    })
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> getAllUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        logger.info("Getting all users - page: {}, size: {}", page, size);
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
            Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<UserResponse> users = userService.getAllUsers(pageable);
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Users retrieved successfully");
        response.put("data", users.getContent());
        response.put("pagination", Map.of(
            "currentPage", users.getNumber(),
            "totalPages", users.getTotalPages(),
            "totalElements", users.getTotalElements(),
            "size", users.getSize(),
            "hasNext", users.hasNext(),
            "hasPrevious", users.hasPrevious()
        ));
        
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Search users",
        description = "Search users by name, email, or phone (Admin only)"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Search completed successfully"
        ),
        @ApiResponse(
            responseCode = "403",
            description = "Access denied"
        )
    })
    @GetMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('SUPER_ADMIN')")
    public ResponseEntity<Map<String, Object>> searchUsers(
            @RequestParam String query,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        logger.info("Searching users with query: {}", query);
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdAt").descending());
        Page<UserResponse> users = userService.searchUsers(query, pageable);
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Search completed successfully");
        response.put("data", users.getContent());
        response.put("pagination", Map.of(
            "currentPage", users.getNumber(),
            "totalPages", users.getTotalPages(),
            "totalElements", users.getTotalElements(),
            "size", users.getSize()
        ));
        
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Update user profile",
        description = "Updates the current user's profile information"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Profile updated successfully",
            content = @Content(mediaType = "application/json", schema = @Schema(implementation = UserResponse.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input data"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User not authenticated"
        )
    })
    @PutMapping("/profile")
    public ResponseEntity<Map<String, Object>> updateProfile(
            @RequestAttribute("userId") UUID userId,
            @Valid @RequestBody UserResponse updateRequest) {

        logger.info("Updating profile for user ID: {}", userId);

        UserResponse updatedUser = userService.updateProfile(userId, updateRequest);
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Profile updated successfully");
        response.put("data", updatedUser);
        
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Update password",
        description = "Updates the current user's password"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Password updated successfully"
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid current password or new password"
        ),
        @ApiResponse(
            responseCode = "401",
            description = "User not authenticated"
        )
    })
    @PutMapping("/password")
    public ResponseEntity<Map<String, Object>> updatePassword(
            @RequestAttribute("userId") UUID userId,
            @Valid @RequestBody UpdatePasswordRequest request) {

        logger.info("Updating password for user ID: {}", userId);

        userService.updatePassword(userId, request);

        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Password updated successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Enable two-factor authentication",
        description = "Enables two-factor authentication for the current user"
    )
    @PostMapping("/2fa/enable")
    public ResponseEntity<Map<String, Object>> enableTwoFactor(@RequestAttribute("userId") UUID userId) {
        logger.info("Enabling 2FA for user ID: {}", userId);

        userService.enableTwoFactor(userId);

        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Two-factor authentication enabled successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Disable two-factor authentication",
        description = "Disables two-factor authentication for the current user"
    )
    @PostMapping("/2fa/disable")
    public ResponseEntity<Map<String, Object>> disableTwoFactor(@RequestAttribute("userId") UUID userId) {
        logger.info("Disabling 2FA for user ID: {}", userId);

        userService.disableTwoFactor(userId);
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Two-factor authentication disabled successfully");
        
        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Set transaction PIN",
        description = "Sets or updates the transaction PIN for the current user"
    )
    @PostMapping("/transaction-pin")
    public ResponseEntity<Map<String, Object>> setTransactionPin(
            @RequestAttribute("userId") UUID userId,
            @RequestBody Map<String, String> pinData) {

        logger.info("Setting transaction PIN for user ID: {}", userId);

        String pin = pinData.get("pin");
        userService.setTransactionPin(userId, pin);

        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Transaction PIN set successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Delete user account",
        description = "Deactivates the current user's account"
    )
    @DeleteMapping("/account")
    public ResponseEntity<Map<String, Object>> deleteAccount(@RequestAttribute("userId") UUID userId) {
        logger.info("Deleting account for user ID: {}", userId);

        userService.deleteAccount(userId);
        
        Map<String, Object> response = new LinkedHashMap<>();
        response.put("success", true);
        response.put("message", "Account deleted successfully");
        
        return ResponseEntity.ok(response);
    }
}
