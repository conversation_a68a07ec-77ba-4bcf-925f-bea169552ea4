package com.aetrustfintech.backend.dto.auth;

import com.aetrustfintech.backend.dto.user.UserResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Authentication response")
public class AuthResponse {

    @Schema(description = "Success status", example = "true")
    private Boolean success;

    @Schema(description = "Response message", example = "Login successful")
    private String message;

    @Schema(description = "JWT access token")
    private String accessToken;

    @Schema(description = "JWT refresh token")
    private String refreshToken;

    @Schema(description = "Token type", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "Token expiration time in milliseconds")
    private Long expiresIn;

    @Schema(description = "User information")
    private UserResponse user;

    @Schema(description = "Requires OTP verification", example = "false")
    private Boolean requiresOtp = false;

    @Schema(description = "OTP identifier for verification")
    private String otpIdentifier;

    public static AuthResponse success(String message, String accessToken, String refreshToken, Long expiresIn, UserResponse user) {
        AuthResponse response = new AuthResponse();
        response.setSuccess(true);
        response.setMessage(message);
        response.setAccessToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(expiresIn);
        response.setUser(user);
        return response;
    }

    public static AuthResponse requiresOtp(String message, String otpIdentifier) {
        AuthResponse response = new AuthResponse();
        response.setSuccess(true);
        response.setMessage(message);
        response.setRequiresOtp(true);
        response.setOtpIdentifier(otpIdentifier);
        return response;
    }

    public static AuthResponse error(String message) {
        AuthResponse response = new AuthResponse();
        response.setSuccess(false);
        response.setMessage(message);
        return response;
    }
}
