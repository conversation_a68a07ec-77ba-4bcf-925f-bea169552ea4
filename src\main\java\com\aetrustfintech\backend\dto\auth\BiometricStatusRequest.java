package com.aetrustfintech.backend.dto.auth;

import jakarta.validation.constraints.NotNull;

public class BiometricStatusRequest {

    @NotNull(message = "Registration ID is required")
    private Long registrationId;

    @NotNull(message = "Biometric enabled status is required")
    private Boolean biometricEnabled;

    private String biometricType; // fingerprint, face, voice

    private String biometricTemplate; // Encrypted biometric template

    private String deviceId; // Device identifier for biometric enrollment

    // Constructors
    public BiometricStatusRequest() {}

    public BiometricStatusRequest(Long registrationId, Boolean biometricEnabled) {
        this.registrationId = registrationId;
        this.biometricEnabled = biometricEnabled;
    }

    public BiometricStatusRequest(Long registrationId, Boolean biometricEnabled, 
                                String biometricType, String biometricTemplate, String deviceId) {
        this.registrationId = registrationId;
        this.biometricEnabled = biometricEnabled;
        this.biometricType = biometricType;
        this.biometricTemplate = biometricTemplate;
        this.deviceId = deviceId;
    }

    // Get<PERSON> and Setters
    public Long getRegistrationId() { return registrationId; }
    public void setRegistrationId(Long registrationId) { this.registrationId = registrationId; }

    public Boolean getBiometricEnabled() { return biometricEnabled; }
    public void setBiometricEnabled(Boolean biometricEnabled) { this.biometricEnabled = biometricEnabled; }

    public String getBiometricType() { return biometricType; }
    public void setBiometricType(String biometricType) { this.biometricType = biometricType; }

    public String getBiometricTemplate() { return biometricTemplate; }
    public void setBiometricTemplate(String biometricTemplate) { this.biometricTemplate = biometricTemplate; }

    public String getDeviceId() { return deviceId; }
    public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
}
