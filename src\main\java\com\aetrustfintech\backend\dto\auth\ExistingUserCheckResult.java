package com.aetrustfintech.backend.dto.auth;

public class ExistingUserCheckResult {
    private final boolean hasExistingUser;
    private final RegistrationStatusResponse response;

    public ExistingUserCheckResult(boolean hasExistingUser, RegistrationStatusResponse response) {
        this.hasExistingUser = hasExistingUser;
        this.response = response;
    }

    public static ExistingUserCheckResult noExistingUser() {
        return new ExistingUserCheckResult(false, null);
    }

    public static ExistingUserCheckResult withExistingUser(RegistrationStatusResponse response) {
        return new ExistingUserCheckResult(true, response);
    }

    public boolean hasExistingUser() {
        return hasExistingUser;
    }

    public RegistrationStatusResponse getResponse() {
        return response;
    }
}
