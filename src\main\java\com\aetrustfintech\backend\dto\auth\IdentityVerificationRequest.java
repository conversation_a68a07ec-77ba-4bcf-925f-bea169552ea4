package com.aetrustfintech.backend.dto.auth;

import com.aetrustfintech.backend.enums.IdType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

public class IdentityVerificationRequest {

    @NotNull(message = "Registration ID is required")
    private Long registrationId;

    @NotNull(message = "ID type is required")
    private IdType idType;

    @NotBlank(message = "ID number is required")
    @Size(min = 5, max = 50, message = "ID number must be between 5 and 50 characters")
    private String idNumber;

    @NotBlank(message = "ID image is required")
    private String idImageBase64; // Base64 encoded image

    // Optional fields for enhanced verification
    private String selfieImageBase64; // Base64 encoded selfie for comparison

    private String address; // Physical address

    @Pattern(regexp = "^[a-zA-Z\\s]+$", message = "Place of birth can only contain letters and spaces")
    private String placeOfBirth;

    private String occupation;

    // Constructors
    public IdentityVerificationRequest() {}

    public IdentityVerificationRequest(Long registrationId, IdType idType, String idNumber, String idImageBase64) {
        this.registrationId = registrationId;
        this.idType = idType;
        this.idNumber = idNumber;
        this.idImageBase64 = idImageBase64;
    }

    // Validation methods
    public boolean isValidEthiopianId() {
        if (idType == IdType.NATIONAL_ID && idNumber != null) {
            // Ethiopian national ID format validation
            return idNumber.matches("^\\d{10}$") || idNumber.matches("^[A-Z]{2}\\d{8}$");
        }
        return true;
    }

    public boolean isValidRwandanId() {
        if (idType == IdType.NATIONAL_ID && idNumber != null) {
            // Rwandan national ID format validation
            return idNumber.matches("^1\\d{15}$");
        }
        return true;
    }

    public boolean isValidPassport() {
        if (idType == IdType.PASSPORT && idNumber != null) {
            // General passport format validation
            return idNumber.matches("^[A-Z0-9]{6,12}$");
        }
        return true;
    }

    // Getters and Setters
    public Long getRegistrationId() { return registrationId; }
    public void setRegistrationId(Long registrationId) { this.registrationId = registrationId; }

    public IdType getIdType() { return idType; }
    public void setIdType(IdType idType) { this.idType = idType; }

    public String getIdNumber() { return idNumber; }
    public void setIdNumber(String idNumber) { this.idNumber = idNumber; }

    public String getIdImageBase64() { return idImageBase64; }
    public void setIdImageBase64(String idImageBase64) { this.idImageBase64 = idImageBase64; }

    public String getSelfieImageBase64() { return selfieImageBase64; }
    public void setSelfieImageBase64(String selfieImageBase64) { this.selfieImageBase64 = selfieImageBase64; }

    public String getAddress() { return address; }
    public void setAddress(String address) { this.address = address; }

    public String getPlaceOfBirth() { return placeOfBirth; }
    public void setPlaceOfBirth(String placeOfBirth) { this.placeOfBirth = placeOfBirth; }

    public String getOccupation() { return occupation; }
    public void setOccupation(String occupation) { this.occupation = occupation; }
}
