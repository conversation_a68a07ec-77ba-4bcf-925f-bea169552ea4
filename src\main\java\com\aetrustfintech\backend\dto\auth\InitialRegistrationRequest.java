package com.aetrustfintech.backend.dto.auth;

import com.aetrustfintech.backend.enums.UserRole;
import jakarta.validation.constraints.*;

import java.time.LocalDate;

public class InitialRegistrationRequest {

    @NotBlank(message = "Platform is required")
    @Pattern(regexp = "^(web|mobile|agent)$", message = "Platform must be web, mobile, or agent")
    private String platform;

    @NotBlank(message = "First name is required")
    @Size(min = 2, max = 50, message = "First name must be between 2 and 50 characters")
    @Pattern(regexp = "^[a-zA-Z\\s]+$", message = "First name can only contain letters and spaces")
    private String firstName;

    @NotBlank(message = "Last name is required")
    @Size(min = 2, max = 50, message = "Last name must be between 2 and 50 characters")
    @Pattern(regexp = "^[a-zA-Z\\s]+$", message = "Last name can only contain letters and spaces")
    private String lastName;

    @NotBlank(message = "Email is required")
    @Email(message = "Please provide a valid email address")
    @Size(max = 100, message = "Email must not exceed 100 characters")
    private String email;

    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "^\\+[1-9]\\d{1,14}$", message = "Phone number must be in international format (+1234567890)")
    private String phone;

    @NotNull(message = "Date of birth is required")
    @Past(message = "Date of birth must be in the past")
    private LocalDate dateOfBirth;

    @NotBlank(message = "Password is required")
    @Size(min = 8, max = 128, message = "Password must be between 8 and 128 characters")
    @Pattern(
        regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]+$",
        message = "Password must contain at least one lowercase letter, one uppercase letter, one digit, and one special character"
    )
    private String password;

    @NotBlank(message = "Country code is required")
    @Pattern(regexp = "^(ET|RW)$", message = "Country code must be ET (Ethiopia) or RW (Rwanda)")
    private String countryCode;

    private UserRole intendedRole = UserRole.USER;

    private String referralCode; // Optional for agent referrals

    // Device and security info (set by controller)
    private String registrationIpAddress;
    private String deviceInfo;
    private String userAgent;

    // Constructors
    public InitialRegistrationRequest() {}

    public InitialRegistrationRequest(String platform, String firstName, String lastName, 
                                    String email, String phone, LocalDate dateOfBirth, 
                                    String password, String countryCode) {
        this.platform = platform;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.phone = phone;
        this.dateOfBirth = dateOfBirth;
        this.password = password;
        this.countryCode = countryCode;
    }

    // Validation methods
    @AssertTrue(message = "User must be at least 18 years old")
    public boolean isValidAge() {
        if (dateOfBirth == null) return false;
        return LocalDate.now().minusYears(18).isAfter(dateOfBirth) || 
               LocalDate.now().minusYears(18).isEqual(dateOfBirth);
    }

    @AssertTrue(message = "User must not be older than 120 years")
    public boolean isReasonableAge() {
        if (dateOfBirth == null) return true;
        return LocalDate.now().minusYears(120).isBefore(dateOfBirth);
    }

    // Helper methods
    public boolean isEthiopian() {
        return "ET".equals(countryCode);
    }

    public boolean isRwandan() {
        return "RW".equals(countryCode);
    }

    public String getFullName() {
        return firstName + " " + lastName;
    }

    // Getters and Setters
    public String getPlatform() { return platform; }
    public void setPlatform(String platform) { this.platform = platform; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public LocalDate getDateOfBirth() { return dateOfBirth; }
    public void setDateOfBirth(LocalDate dateOfBirth) { this.dateOfBirth = dateOfBirth; }

    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }

    public String getCountryCode() { return countryCode; }
    public void setCountryCode(String countryCode) { this.countryCode = countryCode; }

    public UserRole getIntendedRole() { return intendedRole; }
    public void setIntendedRole(UserRole intendedRole) { this.intendedRole = intendedRole; }

    public String getReferralCode() { return referralCode; }
    public void setReferralCode(String referralCode) { this.referralCode = referralCode; }

    public String getRegistrationIpAddress() { return registrationIpAddress; }
    public void setRegistrationIpAddress(String registrationIpAddress) { this.registrationIpAddress = registrationIpAddress; }

    public String getDeviceInfo() { return deviceInfo; }
    public void setDeviceInfo(String deviceInfo) { this.deviceInfo = deviceInfo; }

    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
}
