package com.aetrustfintech.backend.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "User login request")
public class LoginRequest {

    @Schema(description = "Email, phone number, or username", example = "<EMAIL>")
    @NotBlank(message = "Identifier is required")
    private String identifier;

    @Schema(description = "User's password", example = "SecurePass123!")
    @NotBlank(message = "Password is required")
    private String password;

    @Schema(description = "Remember me option", example = "true")
    private Boolean rememberMe = false;

    @Schema(description = "Device information for security tracking")
    private String deviceInfo;

    @Schema(description = "IP address for security tracking")
    private String ipAddress;

    // Custom setter to handle both 'email' and 'identifier' fields from JSON
    @JsonProperty("email")
    public void setEmail(String email) {
        if (email != null && !email.trim().isEmpty()) {
            this.identifier = email;
        }
    }

    // Getter for email (maps to identifier)
    @JsonProperty("email")
    public String getEmail() {
        return this.identifier;
    }
}
