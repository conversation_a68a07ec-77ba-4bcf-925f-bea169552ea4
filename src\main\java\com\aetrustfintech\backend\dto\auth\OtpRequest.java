package com.aetrustfintech.backend.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "OTP verification request")
public class OtpRequest {

    @Schema(description = "Email or phone number", example = "<EMAIL>")
    @NotBlank(message = "Identifier is required")
    private String identifier;

    @Schema(description = "OTP code", example = "123456")
    @NotBlank(message = "OTP code is required")
    @Size(min = 6, max = 6, message = "OTP code must be 6 digits")
    @Pattern(regexp = "^\\d{6}$", message = "OTP code must contain only digits")
    private String otpCode;

    @Schema(description = "OTP type", example = "REGISTRATION")
    @NotBlank(message = "OTP type is required")
    private String otpType;
}
