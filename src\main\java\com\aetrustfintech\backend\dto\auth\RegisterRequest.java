package com.aetrustfintech.backend.dto.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "User registration request")
public class RegisterRequest {

    @Schema(description = "User's email address", example = "<EMAIL>")
    @NotBlank(message = "Email is required")
    @Email(message = "Email must be valid")
    private String email;

    @Schema(description = "User's phone number with country code", example = "+2348012345678")
    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
    private String phone;

    @Schema(description = "User's first name", example = "<PERSON>")
    @NotBlank(message = "First name is required")
    @Size(max = 50, message = "First name cannot exceed 50 characters")
    private String firstName;

    @Schema(description = "User's last name", example = "Doe")
    @NotBlank(message = "Last name is required")
    @Size(max = 50, message = "Last name cannot exceed 50 characters")
    private String lastName;

    @Schema(description = "User's password", example = "SecurePass123!")
    @NotBlank(message = "Password is required")
    @Size(min = 8, message = "Password must be at least 8 characters")
    @Pattern(
        regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$",
        message = "Password must contain at least one uppercase letter, one lowercase letter, one number and one special character"
    )
    private String password;

    @Schema(description = "Password confirmation", example = "SecurePass123!")
    @NotBlank(message = "Password confirmation is required")
    private String confirmPassword;

    @Schema(description = "Country code", example = "NG")
    @Size(min = 2, max = 3, message = "Country code must be 2-3 characters")
    private String countryCode;

    @Schema(description = "Preferred language", example = "en")
    private String languagePreference = "en";

    @Schema(description = "Terms and conditions acceptance", example = "true")
    @AssertTrue(message = "You must accept the terms and conditions")
    private Boolean acceptTerms;

    @Schema(description = "Privacy policy acceptance", example = "true")
    @AssertTrue(message = "You must accept the privacy policy")
    private Boolean acceptPrivacy;

    // Custom validation method
    @AssertTrue(message = "Passwords do not match")
    private boolean isPasswordMatching() {
        return password != null && password.equals(confirmPassword);
    }
}
