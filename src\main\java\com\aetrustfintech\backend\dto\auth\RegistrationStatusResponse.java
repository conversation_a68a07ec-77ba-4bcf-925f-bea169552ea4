package com.aetrustfintech.backend.dto.auth;

import com.aetrustfintech.backend.enums.RegistrationStep;
import com.aetrustfintech.backend.enums.VerificationStatus;

import java.time.LocalDateTime;

public class RegistrationStatusResponse {

    private Long registrationId;
    private String firstName;
    private String lastName;
    private String email;
    private String phone;
    private String countryCode;
    private RegistrationStep currentStep;
    private RegistrationStep nextStep;
    private Boolean canProceed;
    private Boolean isCompleted;
    private Double completionPercentage;
    private String message;
    private String nextAction;

    // Step statuses
    private VerificationStatus phoneVerificationStatus;
    private VerificationStatus emailVerificationStatus;
    private VerificationStatus idVerificationStatus;
    private Boolean pinSet;
    private Boolean biometricEnabled;

    // Timestamps
    private LocalDateTime phoneVerifiedAt;
    private LocalDateTime emailVerifiedAt;
    private LocalDateTime idVerifiedAt;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public RegistrationStatusResponse() {}

    public RegistrationStatusResponse(Long registrationId, String firstName, String lastName,
                                    String email, String phone, String countryCode,
                                    RegistrationStep currentStep, RegistrationStep nextStep,
                                    Boolean canProceed, Boolean isCompleted) {
        this.registrationId = registrationId;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.phone = phone;
        this.countryCode = countryCode;
        this.currentStep = currentStep;
        this.nextStep = nextStep;
        this.canProceed = canProceed;
        this.isCompleted = isCompleted;
        this.completionPercentage = calculateCompletionPercentage();
    }

    // Helper methods
    private Double calculateCompletionPercentage() {
        if (isCompleted != null && isCompleted) return 100.0;

        int totalSteps = 5; // phone, email, id, pin, biometric
        int completedSteps = 0;

        if (phoneVerificationStatus == VerificationStatus.VERIFIED) completedSteps++;
        if (emailVerificationStatus == VerificationStatus.VERIFIED) completedSteps++;
        // ID verification counts as completed if VERIFIED or UNDER_REVIEW
        if (idVerificationStatus == VerificationStatus.VERIFIED ||
            idVerificationStatus == VerificationStatus.UNDER_REVIEW) completedSteps++;
        if (pinSet != null && pinSet) completedSteps++;
        if (biometricEnabled != null) completedSteps++; // Optional step

        return (completedSteps * 100.0) / totalSteps;
    }

    public String getStepDescription() {
        if (currentStep == null) return "Unknown step";
        
        return switch (currentStep) {
            case PHONE_VERIFICATION -> "Verify your phone number";
            case EMAIL_VERIFICATION -> "Verify your email address";
            case IDENTITY_VERIFICATION -> "Upload and verify your ID document";
            case TRANSACTION_PIN -> "Set your transaction PIN";
            case BUSINESS_VERIFICATION -> "Complete business verification";
            case COMPLETED -> "Registration completed";
        };
    }

    public String getNextStepDescription() {
        if (nextStep == null) return "No next step";
        
        return switch (nextStep) {
            case PHONE_VERIFICATION -> "Verify your phone number";
            case EMAIL_VERIFICATION -> "Verify your email address";
            case IDENTITY_VERIFICATION -> "Upload and verify your ID document";
            case TRANSACTION_PIN -> "Set your transaction PIN";
            case BUSINESS_VERIFICATION -> "Complete business verification";
            case COMPLETED -> "Complete registration";
        };
    }

    // Getters and Setters
    public Long getRegistrationId() { return registrationId; }
    public void setRegistrationId(Long registrationId) { this.registrationId = registrationId; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public String getCountryCode() { return countryCode; }
    public void setCountryCode(String countryCode) { this.countryCode = countryCode; }

    public RegistrationStep getCurrentStep() { return currentStep; }
    public void setCurrentStep(RegistrationStep currentStep) { 
        this.currentStep = currentStep;
        this.completionPercentage = calculateCompletionPercentage();
    }

    public RegistrationStep getNextStep() { return nextStep; }
    public void setNextStep(RegistrationStep nextStep) { this.nextStep = nextStep; }

    public Boolean getCanProceed() { return canProceed; }
    public void setCanProceed(Boolean canProceed) { this.canProceed = canProceed; }

    public Boolean getIsCompleted() { return isCompleted; }
    public void setIsCompleted(Boolean isCompleted) { 
        this.isCompleted = isCompleted;
        this.completionPercentage = calculateCompletionPercentage();
    }

    public Double getCompletionPercentage() { return completionPercentage; }
    public void setCompletionPercentage(Double completionPercentage) { this.completionPercentage = completionPercentage; }

    public VerificationStatus getPhoneVerificationStatus() { return phoneVerificationStatus; }
    public void setPhoneVerificationStatus(VerificationStatus phoneVerificationStatus) { 
        this.phoneVerificationStatus = phoneVerificationStatus;
        this.completionPercentage = calculateCompletionPercentage();
    }

    public VerificationStatus getEmailVerificationStatus() { return emailVerificationStatus; }
    public void setEmailVerificationStatus(VerificationStatus emailVerificationStatus) { 
        this.emailVerificationStatus = emailVerificationStatus;
        this.completionPercentage = calculateCompletionPercentage();
    }

    public VerificationStatus getIdVerificationStatus() { return idVerificationStatus; }
    public void setIdVerificationStatus(VerificationStatus idVerificationStatus) { 
        this.idVerificationStatus = idVerificationStatus;
        this.completionPercentage = calculateCompletionPercentage();
    }

    public Boolean getPinSet() { return pinSet; }
    public void setPinSet(Boolean pinSet) { 
        this.pinSet = pinSet;
        this.completionPercentage = calculateCompletionPercentage();
    }

    public Boolean getBiometricEnabled() { return biometricEnabled; }
    public void setBiometricEnabled(Boolean biometricEnabled) { 
        this.biometricEnabled = biometricEnabled;
        this.completionPercentage = calculateCompletionPercentage();
    }

    public LocalDateTime getPhoneVerifiedAt() { return phoneVerifiedAt; }
    public void setPhoneVerifiedAt(LocalDateTime phoneVerifiedAt) { this.phoneVerifiedAt = phoneVerifiedAt; }

    public LocalDateTime getEmailVerifiedAt() { return emailVerifiedAt; }
    public void setEmailVerifiedAt(LocalDateTime emailVerifiedAt) { this.emailVerifiedAt = emailVerifiedAt; }

    public LocalDateTime getIdVerifiedAt() { return idVerifiedAt; }
    public void setIdVerifiedAt(LocalDateTime idVerifiedAt) { this.idVerifiedAt = idVerifiedAt; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }

    public String getNextAction() { return nextAction; }
    public void setNextAction(String nextAction) { this.nextAction = nextAction; }
}
