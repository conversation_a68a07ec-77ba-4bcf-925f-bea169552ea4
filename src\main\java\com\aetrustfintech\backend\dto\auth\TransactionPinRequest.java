package com.aetrustfintech.backend.dto.auth;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

public class TransactionPinRequest {

    @NotNull(message = "Registration ID is required")
    private Long registrationId;

    @NotBlank(message = "Transaction PIN is required")
    @Size(min = 4, max = 6, message = "Transaction PIN must be between 4 and 6 digits")
    @Pattern(regexp = "^\\d{4,6}$", message = "Transaction PIN must contain only digits")
    private String pin;

    @NotBlank(message = "PIN confirmation is required")
    private String confirmPin;

    // Constructors
    public TransactionPinRequest() {}

    public TransactionPinRequest(Long registrationId, String pin, String confirmPin) {
        this.registrationId = registrationId;
        this.pin = pin;
        this.confirmPin = confirmPin;
    }

    // Validation methods
    public boolean isPinMatching() {
        return pin != null && pin.equals(confirmPin);
    }

    public boolean isSecurePin() {
        if (pin == null || pin.length() < 4) return false;
        
        // Check for sequential numbers
        boolean isSequential = true;
        for (int i = 1; i < pin.length(); i++) {
            if (Character.getNumericValue(pin.charAt(i)) != 
                Character.getNumericValue(pin.charAt(i-1)) + 1) {
                isSequential = false;
                break;
            }
        }
        
        // Check for repeated digits
        boolean hasRepeatedDigits = pin.chars()
            .distinct()
            .count() < pin.length() / 2;
        
        // Check for common weak PINs
        String[] weakPins = {"1234", "0000", "1111", "2222", "3333", "4444", 
                           "5555", "6666", "7777", "8888", "9999", "1122", "1212"};
        boolean isWeakPin = false;
        for (String weakPin : weakPins) {
            if (pin.equals(weakPin)) {
                isWeakPin = true;
                break;
            }
        }
        
        return !isSequential && !hasRepeatedDigits && !isWeakPin;
    }

    // Getters and Setters
    public Long getRegistrationId() { return registrationId; }
    public void setRegistrationId(Long registrationId) { this.registrationId = registrationId; }

    public String getPin() { return pin; }
    public void setPin(String pin) { this.pin = pin; }

    public String getConfirmPin() { return confirmPin; }
    public void setConfirmPin(String confirmPin) { this.confirmPin = confirmPin; }
}
