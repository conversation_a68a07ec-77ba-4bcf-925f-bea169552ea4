package com.aetrustfintech.backend.dto.compliance;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;


public class ComplianceCheckRequest {
    
    @NotBlank(message = "Country is required")
    private String country;
    
    private String accountLevel;
    private String userType;
    
    @NotNull(message = "Current balance is required")
    private BigDecimal currentBalance;
    
    @NotNull(message = "Transaction amount is required")
    private BigDecimal transactionAmount;
    
    private BigDecimal dailyVolume;
    private BigDecimal monthlyVolume;

    public ComplianceCheckRequest() {}

    public ComplianceCheckRequest(String country, String accountLevel, String userType,
                                BigDecimal currentBalance, BigDecimal transactionAmount,
                                BigDecimal dailyVolume, BigDecimal monthlyVolume) {
        this.country = country;
        this.accountLevel = accountLevel;
        this.userType = userType;
        this.currentBalance = currentBalance;
        this.transactionAmount = transactionAmount;
        this.dailyVolume = dailyVolume;
        this.monthlyVolume = monthlyVolume;
    }

    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }

    public String getAccountLevel() { return accountLevel; }
    public void setAccountLevel(String accountLevel) { this.accountLevel = accountLevel; }

    public String getUserType() { return userType; }
    public void setUserType(String userType) { this.userType = userType; }

    public BigDecimal getCurrentBalance() { return currentBalance; }
    public void setCurrentBalance(BigDecimal currentBalance) { this.currentBalance = currentBalance; }

    public BigDecimal getTransactionAmount() { return transactionAmount; }
    public void setTransactionAmount(BigDecimal transactionAmount) { this.transactionAmount = transactionAmount; }

    public BigDecimal getDailyVolume() { return dailyVolume; }
    public void setDailyVolume(BigDecimal dailyVolume) { this.dailyVolume = dailyVolume; }

    public BigDecimal getMonthlyVolume() { return monthlyVolume; }
    public void setMonthlyVolume(BigDecimal monthlyVolume) { this.monthlyVolume = monthlyVolume; }
}
