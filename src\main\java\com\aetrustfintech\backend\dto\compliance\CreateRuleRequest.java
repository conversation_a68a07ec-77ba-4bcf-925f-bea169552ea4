package com.aetrustfintech.backend.dto.compliance;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;


public class CreateRuleRequest {
    
    @NotBlank(message = "Country is required")
    private String country;
    
    @NotBlank(message = "Rule type is required")
    private String ruleType;
    
    private String accountLevel;
    private String userType;
    
    @NotBlank(message = "Value is required")
    private String value;
    
    @NotBlank(message = "Rule name is required")
    private String ruleName;
    
    private String description;

    public CreateRuleRequest() {}

    public CreateRuleRequest(String country, String ruleType, String accountLevel, 
                           String userType, String value, String ruleName, String description) {
        this.country = country;
        this.ruleType = ruleType;
        this.accountLevel = accountLevel;
        this.userType = userType;
        this.value = value;
        this.ruleName = ruleName;
        this.description = description;
    }

    public String getCountry() { return country; }
    public void setCountry(String country) { this.country = country; }

    public String getRuleType() { return ruleType; }
    public void setRuleType(String ruleType) { this.ruleType = ruleType; }

    public String getAccountLevel() { return accountLevel; }
    public void setAccountLevel(String accountLevel) { this.accountLevel = accountLevel; }

    public String getUserType() { return userType; }
    public void setUserType(String userType) { this.userType = userType; }

    public String getValue() { return value; }
    public void setValue(String value) { this.value = value; }

    public String getRuleName() { return ruleName; }
    public void setRuleName(String ruleName) { this.ruleName = ruleName; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
}
