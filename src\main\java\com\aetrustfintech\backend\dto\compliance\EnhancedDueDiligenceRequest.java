package com.aetrustfintech.backend.dto.compliance;

import jakarta.validation.constraints.NotBlank;

public class EnhancedDueDiligenceRequest {
    
    @NotBlank(message = "Business license number is required")
    private String businessLicenseNumber;
    
    private String additionalDocuments;
    private String sourceOfFunds;
    private String businessNature;

    public EnhancedDueDiligenceRequest() {}

    public EnhancedDueDiligenceRequest(String businessLicenseNumber) {
        this.businessLicenseNumber = businessLicenseNumber;
    }

    public String getBusinessLicenseNumber() { return businessLicenseNumber; }
    public void setBusinessLicenseNumber(String businessLicenseNumber) { 
        this.businessLicenseNumber = businessLicenseNumber; 
    }

    public String getAdditionalDocuments() { return additionalDocuments; }
    public void setAdditionalDocuments(String additionalDocuments) { 
        this.additionalDocuments = additionalDocuments; 
    }

    public String getSourceOfFunds() { return sourceOfFunds; }
    public void setSourceOfFunds(String sourceOfFunds) { this.sourceOfFunds = sourceOfFunds; }

    public String getBusinessNature() { return businessNature; }
    public void setBusinessNature(String businessNature) { this.businessNature = businessNature; }
}
