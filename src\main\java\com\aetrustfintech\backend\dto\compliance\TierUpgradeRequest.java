package com.aetrustfintech.backend.dto.compliance;

import jakarta.validation.constraints.NotBlank;

public class TierUpgradeRequest {
    
    @NotBlank(message = "Target tier is required")
    private String targetTier;
    
    private String businessLicenseNumber;
    private String additionalDocuments;

    public TierUpgradeRequest() {}

    public TierUpgradeRequest(String targetTier) {
        this.targetTier = targetTier;
    }

    public String getTargetTier() { return targetTier; }
    public void setTargetTier(String targetTier) { this.targetTier = targetTier; }

    public String getBusinessLicenseNumber() { return businessLicenseNumber; }
    public void setBusinessLicenseNumber(String businessLicenseNumber) { 
        this.businessLicenseNumber = businessLicenseNumber; 
    }

    public String getAdditionalDocuments() { return additionalDocuments; }
    public void setAdditionalDocuments(String additionalDocuments) { 
        this.additionalDocuments = additionalDocuments; 
    }
}
