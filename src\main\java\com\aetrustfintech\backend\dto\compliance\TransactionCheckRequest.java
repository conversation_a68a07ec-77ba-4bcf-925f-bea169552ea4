package com.aetrustfintech.backend.dto.compliance;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;


public class TransactionCheckRequest {
    
    @NotNull(message = "Transaction amount is required")
    private BigDecimal transactionAmount;
    
    @NotBlank(message = "Transaction type is required")
    private String transactionType;

    public TransactionCheckRequest() {}

    public TransactionCheckRequest(BigDecimal transactionAmount, String transactionType) {
        this.transactionAmount = transactionAmount;
        this.transactionType = transactionType;
    }

    public BigDecimal getTransactionAmount() { return transactionAmount; }
    public void setTransactionAmount(BigDecimal transactionAmount) { this.transactionAmount = transactionAmount; }

    public String getTransactionType() { return transactionType; }
    public void setTransactionType(String transactionType) { this.transactionType = transactionType; }
}
