package com.aetrustfintech.backend.dto.compliance;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

public class TransactionLimitCheckRequest {
    
    @NotNull(message = "Transaction amount is required")
    @Positive(message = "Transaction amount must be positive")
    private BigDecimal transactionAmount;
    
    private String transactionType;
    private BigDecimal currentDailyVolume;
    private BigDecimal currentMonthlyVolume;

    public TransactionLimitCheckRequest() {}

    public TransactionLimitCheckRequest(BigDecimal transactionAmount, String transactionType) {
        this.transactionAmount = transactionAmount;
        this.transactionType = transactionType;
    }

    public BigDecimal getTransactionAmount() { return transactionAmount; }
    public void setTransactionAmount(BigDecimal transactionAmount) { 
        this.transactionAmount = transactionAmount; 
    }

    public String getTransactionType() { return transactionType; }
    public void setTransactionType(String transactionType) { 
        this.transactionType = transactionType; 
    }

    public BigDecimal getCurrentDailyVolume() { return currentDailyVolume; }
    public void setCurrentDailyVolume(BigDecimal currentDailyVolume) { 
        this.currentDailyVolume = currentDailyVolume; 
    }

    public BigDecimal getCurrentMonthlyVolume() { return currentMonthlyVolume; }
    public void setCurrentMonthlyVolume(BigDecimal currentMonthlyVolume) { 
        this.currentMonthlyVolume = currentMonthlyVolume; 
    }
}
