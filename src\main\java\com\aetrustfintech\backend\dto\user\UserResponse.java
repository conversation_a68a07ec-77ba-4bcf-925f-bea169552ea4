package com.aetrustfintech.backend.dto.user;

import com.aetrustfintech.backend.enums.AccountStatus;
import com.aetrustfintech.backend.enums.UserRole;
import com.aetrustfintech.backend.enums.VerificationStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "User response data")
public class UserResponse {

    @Schema(description = "User ID", example = "550e8400-e29b-41d4-a716-************")
    private UUID id;

    @Schema(description = "Registration ID", example = "1")
    private Long registrationId;

    @Schema(description = "Email address", example = "<EMAIL>")
    private String email;

    @Schema(description = "Phone number", example = "+*************")
    private String phone;

    @Schema(description = "Username", example = "johndoe")
    private String username;

    @Schema(description = "First name", example = "John")
    private String firstName;

    @Schema(description = "Last name", example = "Doe")
    private String lastName;

    @Schema(description = "Full name", example = "John Doe")
    private String fullName;

    @Schema(description = "Profile picture URL")
    private String profilePicture;

    @Schema(description = "User bio")
    private String bio;

    @Schema(description = "User address")
    private String address;

    @Schema(description = "User role")
    private UserRole role;

    @Schema(description = "Account status")
    private AccountStatus accountStatus;

    @Schema(description = "KYC status")
    private String kycStatus;

    @Schema(description = "Email verification status")
    private VerificationStatus emailVerificationStatus;

    @Schema(description = "Phone verification status")
    private VerificationStatus phoneVerificationStatus;

    @Schema(description = "Is verified", example = "true")
    private Boolean isVerified;

    @Schema(description = "Wallet balance", example = "1000.00")
    private BigDecimal walletBalance;

    @Schema(description = "Country code", example = "NG")
    private String countryCode;

    @Schema(description = "Currency code", example = "NGN")
    private String currencyCode;

    @Schema(description = "Language preference", example = "en")
    private String languagePreference;

    @Schema(description = "Two factor authentication enabled", example = "false")
    private Boolean twoFactorEnabled;

    @Schema(description = "Biometric authentication enabled", example = "false")
    private Boolean biometricEnabled;

    @Schema(description = "Transaction PIN set", example = "false")
    private Boolean pinSet;

    @Schema(description = "Last login timestamp")
    private LocalDateTime lastLogin;

    @Schema(description = "Email verified timestamp")
    private LocalDateTime emailVerifiedAt;

    @Schema(description = "Phone verified timestamp")
    private LocalDateTime phoneVerifiedAt;

    @Schema(description = "KYC completed timestamp")
    private LocalDateTime kycCompletedAt;

    @Schema(description = "Account creation timestamp")
    private LocalDateTime createdAt;

    @Schema(description = "Last update timestamp")
    private LocalDateTime updatedAt;
}
