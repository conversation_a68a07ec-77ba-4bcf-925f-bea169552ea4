package com.aetrustfintech.backend.enums;

/**
 * Supported countries for KYC compliance
 */
public enum Country {
    RWANDA("RW", "Rwanda", "RWF", "Rwandan Franc"),
    ETHIOPIA("ET", "Ethiopia", "ETB", "Ethiopian Birr");

    private final String code;
    private final String name;
    private final String currencyCode;
    private final String currencyName;

    Country(String code, String name, String currencyCode, String currencyName) {
        this.code = code;
        this.name = name;
        this.currencyCode = currencyCode;
        this.currencyName = currencyName;
    }

    public String getCode() { return code; }
    public String getName() { return name; }
    public String getCurrencyCode() { return currencyCode; }
    public String getCurrencyName() { return currencyName; }

    public static Country fromCode(String code) {
        for (Country country : values()) {
            if (country.code.equalsIgnoreCase(code)) {
                return country;
            }
        }
        throw new IllegalArgumentException("Unknown country code: " + code);
    }

    @Override
    public String toString() {
        return code;
    }
}
