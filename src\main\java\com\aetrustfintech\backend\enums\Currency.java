package com.aetrustfintech.backend.enums;

public enum Currency {
    ETB("ETB"), // Ethiopian Birr - Primary for Ethiopia
    RWF("RWF"), // Rwandan Franc - Primary for Rwanda
    USD("USD"), // US Dollar - International
    EUR("EUR"), // Euro - International
    GBP("GBP"), // British Pound - International
    KES("KES"), // Kenyan Shilling - Regional
    UGX("UGX"), // Ugandan Shilling - Regional
    TZS("TZS"); // Tanzanian Shilling - Regional
    
    private final String value;
    
    Currency(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
