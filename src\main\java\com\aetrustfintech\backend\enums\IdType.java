package com.aetrustfintech.backend.enums;

public enum IdType {
    NATIONAL_ID("national_id"),
    PASSPORT("passport"),
    DRIVERS_LICENSE("drivers_license"),
    VOTERS_CARD("voters_card");
    
    private final String value;
    
    IdType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
