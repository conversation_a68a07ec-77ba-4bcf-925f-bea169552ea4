package com.aetrustfintech.backend.enums;

/**
 * KYC levels for different countries and compliance requirements
 */
public enum KycLevel {
    // Ethiopia NBE levels
    LEVEL_0("LEVEL_0", "Basic registration", 100.00),
    LEVEL_1("LEVEL_1", "Email/Phone verified", 1000.00),
    LEVEL_2("LEVEL_2", "ID verification", 10000.00),
    LEVEL_3("LEVEL_3", "Full KYC", 100000.00),
    
    // Rwanda BNR tiers
    TIER_I("TIER_I", "Basic tier", 1000.00),
    TIER_II("TIER_II", "Enhanced tier", 10000.00),
    TIER_III("TIER_III", "Full tier", 100000.00),
    
    // Agent levels (no limits)
    AGENT("AGENT", "Agent account", Double.MAX_VALUE),
    MERCHANT("MERCHANT", "Merchant account", Double.MAX_VALUE);

    private final String value;
    private final String description;
    private final Double dailyLimit;

    KycLevel(String value, String description, Double dailyLimit) {
        this.value = value;
        this.description = description;
        this.dailyLimit = dailyLimit;
    }

    public String getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public Double getDailyLimit() {
        return dailyLimit;
    }

    @Override
    public String toString() {
        return value;
    }

    public static KycLevel fromValue(String value) {
        for (KycLevel level : KycLevel.values()) {
            if (level.value.equals(value)) {
                return level;
            }
        }
        throw new IllegalArgumentException("Unknown KYC level: " + value);
    }

    public boolean isAgent() {
        return this == AGENT;
    }

    public boolean isMerchant() {
        return this == MERCHANT;
    }

    public boolean hasUnlimitedTransactions() {
        return this == AGENT || this == MERCHANT;
    }
}
