package com.aetrustfintech.backend.enums;

public enum RegistrationStep {
    PHONE_VERIFICATION("phone_verification"),
    EMAIL_VERIFICATION("email_verification"),
    IDENTITY_VERIFICATION("identity_verification"),
    TRANSACTION_PIN("transaction_pin"),
    BUSINESS_VERIFICATION("business_verification"),
    COMPLETED("completed");
    
    private final String value;
    
    RegistrationStep(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    @Override
    public String toString() {
        return value;
    }
}
