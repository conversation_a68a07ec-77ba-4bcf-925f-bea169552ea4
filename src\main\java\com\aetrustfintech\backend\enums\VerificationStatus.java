package com.aetrustfintech.backend.enums;

public enum VerificationStatus {
    PENDING("pending"),
    VERIFIED("verified"),
    FAILED("failed"),
    EXPIRED("expired"),
    UNDER_REVIEW("under_review"),
    REJECTED("rejected");

    private final String value;

    VerificationStatus(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return value;
    }

    public static VerificationStatus fromValue(String value) {
        for (VerificationStatus status : VerificationStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown verification status: " + value);
    }

    public boolean isVerified() {
        return this == VERIFIED;
    }

    public boolean isPending() {
        return this == PENDING;
    }

    public boolean isFailed() {
        return this == FAILED || this == REJECTED || this == EXPIRED;
    }

    public boolean isUnderReview() {
        return this == UNDER_REVIEW;
    }
}
