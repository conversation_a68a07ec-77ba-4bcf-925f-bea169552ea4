package com.aetrustfintech.backend.model;

import com.aetrustfintech.backend.enums.AccountStatus;

import com.aetrustfintech.backend.enums.UserRole;
import com.aetrustfintech.backend.enums.VerificationStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "users", indexes = {
    @Index(name = "idx_user_email", columnList = "email"),
    @Index(name = "idx_user_phone", columnList = "phone"),
    @Index(name = "idx_user_username", columnList = "username")
})
public class User {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @Column(name = "registration_id")
    private Long registrationId;

    @NotBlank(message = "Email is required")
    @Email(message = "Email must be valid")
    @Column(unique = true, nullable = false)
    private String email;

    @NotBlank(message = "Phone number is required")
    @Pattern(regexp = "^\\+?[1-9]\\d{1,14}$", message = "Invalid phone number format")
    @Column(unique = true, nullable = false)
    private String phone;

    @Column(unique = true)
    @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    private String username;

    @NotBlank(message = "First name is required")
    @Size(max = 50, message = "First name cannot exceed 50 characters")
    @Column(name = "first_name")
    private String firstName;

    @NotBlank(message = "Last name is required")
    @Size(max = 50, message = "Last name cannot exceed 50 characters")
    @Column(name = "last_name")
    private String lastName;

    @JsonIgnore
    @NotBlank(message = "Password is required")
    @Size(min = 8, message = "Password must be at least 8 characters")
    private String password;

    @Column(name = "profile_picture")
    private String profilePicture;

    @Column(columnDefinition = "TEXT")
    private String bio;

    @Column(columnDefinition = "TEXT")
    private String address;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserRole role = UserRole.USER;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_status", nullable = false)
    private AccountStatus accountStatus = AccountStatus.ACTIVE;

    @Column(name = "kyc_status")
    private String kycStatus = "PENDING";

    @Enumerated(EnumType.STRING)
    @Column(name = "email_verification_status", nullable = false)
    private VerificationStatus emailVerificationStatus = VerificationStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(name = "phone_verification_status", nullable = false)
    private VerificationStatus phoneVerificationStatus = VerificationStatus.PENDING;

    @Column(name = "is_verified", nullable = false)
    private Boolean isVerified = false;

    @Column(name = "wallet_balance", precision = 19, scale = 2)
    private BigDecimal walletBalance = BigDecimal.ZERO;

    @Column(name = "agent_info", columnDefinition = "TEXT")
    private String agentInfo;

    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;

    @Column(name = "country_code", length = 3)
    private String countryCode;

    @Column(name = "currency_code", length = 3)
    private String currencyCode = "NGN";

    @Column(name = "language_preference", length = 10)
    private String languagePreference = "en";

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "two_factor_enabled", nullable = false)
    private Boolean twoFactorEnabled = false;

    @Column(name = "biometric_enabled", nullable = false)
    private Boolean biometricEnabled = false;

    @Column(name = "pin_set", nullable = false)
    private Boolean pinSet = false;

    @JsonIgnore
    @Column(name = "transaction_pin")
    private String transactionPin;

    @Column(name = "last_login")
    private LocalDateTime lastLogin;

    @Column(name = "login_attempts", nullable = false)
    private Integer loginAttempts = 0;

    @Column(name = "account_locked_until")
    private LocalDateTime accountLockedUntil;

    @Column(name = "email_verified_at")
    private LocalDateTime emailVerifiedAt;

    @Column(name = "phone_verified_at")
    private LocalDateTime phoneVerifiedAt;

    @Column(name = "kyc_completed_at")
    private LocalDateTime kycCompletedAt;

    @Column(name = "password_changed_at")
    private LocalDateTime passwordChangedAt;

    @Column(name = "country_specific_data", columnDefinition = "TEXT")
    private String countrySpecificData;

    @Column(name = "initialized_at")
    private LocalDateTime initializedAt;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "daily_transactions_used")
    private BigDecimal dailyTransactionsUsed = BigDecimal.ZERO;

    @Column(name = "monthly_transactions_used")
    private BigDecimal monthlyTransactionsUsed = BigDecimal.ZERO;

    @Column(name = "last_daily_reset")
    private LocalDate lastDailyReset;

    @Column(name = "last_monthly_reset")
    private LocalDate lastMonthlyReset;

    @Column(name = "account_dormant")
    private Boolean accountDormant = false;

    @Column(name = "last_activity_date")
    private LocalDateTime lastActivityDate;

    @Column(name = "dormancy_notification_sent")
    private Boolean dormancyNotificationSent = false;

    public String getFullName() {
        return firstName + " " + lastName;
    }

    public boolean isAccountLocked() {
        return accountLockedUntil != null && accountLockedUntil.isAfter(LocalDateTime.now());
    }

    public boolean isEmailVerified() {
        return emailVerificationStatus == VerificationStatus.VERIFIED;
    }

    public boolean isPhoneVerified() {
        return phoneVerificationStatus == VerificationStatus.VERIFIED;
    }

    public boolean canPerformTransactions() {
        return accountStatus == AccountStatus.ACTIVE &&
               !isAccountLocked() &&
               isEmailVerified() &&
               isPhoneVerified();
    }

    public boolean isEthiopian() {
        return "ET".equals(countryCode);
    }

    public boolean isRwandan() {
        return "RW".equals(countryCode);
    }

    public boolean canTransact(BigDecimal amount) {
        if (!canPerformTransactions()) return false;

        BigDecimal dailyLimit = getDailyTransactionLimit();
        BigDecimal monthlyLimit = getMonthlyTransactionLimit();

        return (dailyTransactionsUsed.add(amount).compareTo(dailyLimit) <= 0) &&
               (monthlyTransactionsUsed.add(amount).compareTo(monthlyLimit) <= 0);
    }

    public BigDecimal getDailyTransactionLimit() {
        // Basic limits for auth service - detailed limits handled by wallet service
        return new BigDecimal("1000.00");
    }

    public BigDecimal getMonthlyTransactionLimit() {
        // Basic limits for auth service - detailed limits handled by wallet service
        return new BigDecimal("10000.00");
    }

    public BigDecimal getMaxWalletBalance() {
        // Basic limits for auth service - detailed limits handled by wallet service
        return new BigDecimal("5000.00");
    }

    public boolean isDormant() {
        if (lastActivityDate == null) return false;
        return lastActivityDate.isBefore(LocalDateTime.now().minusMonths(12));
    }

    public void updateActivity() {
        this.lastActivityDate = LocalDateTime.now();
        this.accountDormant = false;
    }

    // Getters and Setters for new fields
    public LocalDate getDateOfBirth() { return dateOfBirth; }
    public void setDateOfBirth(LocalDate dateOfBirth) { this.dateOfBirth = dateOfBirth; }

    public BigDecimal getDailyTransactionsUsed() { return dailyTransactionsUsed; }
    public void setDailyTransactionsUsed(BigDecimal dailyTransactionsUsed) { this.dailyTransactionsUsed = dailyTransactionsUsed; }

    public BigDecimal getMonthlyTransactionsUsed() { return monthlyTransactionsUsed; }
    public void setMonthlyTransactionsUsed(BigDecimal monthlyTransactionsUsed) { this.monthlyTransactionsUsed = monthlyTransactionsUsed; }

    public LocalDate getLastDailyReset() { return lastDailyReset; }
    public void setLastDailyReset(LocalDate lastDailyReset) { this.lastDailyReset = lastDailyReset; }

    public LocalDate getLastMonthlyReset() { return lastMonthlyReset; }
    public void setLastMonthlyReset(LocalDate lastMonthlyReset) { this.lastMonthlyReset = lastMonthlyReset; }

    public Boolean getAccountDormant() { return accountDormant; }
    public void setAccountDormant(Boolean accountDormant) { this.accountDormant = accountDormant; }

    public LocalDateTime getLastActivityDate() { return lastActivityDate; }
    public void setLastActivityDate(LocalDateTime lastActivityDate) { this.lastActivityDate = lastActivityDate; }

    public Boolean getDormancyNotificationSent() { return dormancyNotificationSent; }
    public void setDormancyNotificationSent(Boolean dormancyNotificationSent) { this.dormancyNotificationSent = dormancyNotificationSent; }
}
