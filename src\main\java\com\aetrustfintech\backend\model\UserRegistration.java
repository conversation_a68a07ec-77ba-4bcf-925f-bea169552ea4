package com.aetrustfintech.backend.model;

import com.aetrustfintech.backend.enums.RegistrationStep;
import com.aetrustfintech.backend.enums.UserRole;
import com.aetrustfintech.backend.enums.VerificationStatus;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "user_registrations")
public class UserRegistration {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    private String platform; // web, mobile, agent

    @Column(nullable = false)
    private String firstName;

    @Column(nullable = false)
    private String lastName;

    @Column(unique = true, nullable = false)
    private String email;

    @Column(unique = true, nullable = false)
    private String phone;

    @Column(nullable = false)
    private LocalDate dateOfBirth;

    @Column(nullable = false)
    private String passwordHash; // Hashed password

    @Column(nullable = false, length = 2)
    private String countryCode; // ET, RW

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private UserRole intendedRole = UserRole.USER;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private RegistrationStep currentStep = RegistrationStep.PHONE_VERIFICATION;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private VerificationStatus phoneVerificationStatus = VerificationStatus.PENDING;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private VerificationStatus emailVerificationStatus = VerificationStatus.PENDING;

    @Column
    private LocalDateTime phoneVerifiedAt;

    @Column
    private LocalDateTime emailVerifiedAt;

    @Column
    private String idType; // From IdType enum

    @Column
    private String idNumberHash; // Hashed ID number for security

    @Column
    private String idImagePath; // Path to uploaded ID image

    @Enumerated(EnumType.STRING)
    @Column
    private VerificationStatus idVerificationStatus = VerificationStatus.PENDING;

    @Column
    private LocalDateTime idVerifiedAt;

    @Column
    private String transactionPinHash; // Hashed transaction PIN

    @Column
    private Boolean pinSet = false;

    @Column
    private Boolean biometricEnabled = false;

    @Column
    private String biometricData; // Encrypted biometric template

    @Column
    private String registrationIpAddress;

    @Column
    private String deviceInfo;

    @Column
    private String userAgent;

    @Column
    private String referralCode; // For agent referrals

    @Column
    private Boolean isCompleted = false;

    @Column
    private UUID completedUserId; // Reference to created User entity

    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;

    // Constructors
    public UserRegistration() {}

    public UserRegistration(String platform, String firstName, String lastName, 
                          String email, String phone, LocalDate dateOfBirth, 
                          String passwordHash, String countryCode) {
        this.platform = platform;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.phone = phone;
        this.dateOfBirth = dateOfBirth;
        this.passwordHash = passwordHash;
        this.countryCode = countryCode;
    }

    // Helper methods
    public boolean canProceedToNextStep() {
        return switch (currentStep) {
            case PHONE_VERIFICATION -> phoneVerificationStatus == VerificationStatus.VERIFIED;
            case EMAIL_VERIFICATION -> emailVerificationStatus == VerificationStatus.VERIFIED;
            case IDENTITY_VERIFICATION -> idVerificationStatus == VerificationStatus.VERIFIED ||
                                        idVerificationStatus == VerificationStatus.UNDER_REVIEW;
            case TRANSACTION_PIN -> pinSet;
            case BUSINESS_VERIFICATION -> true; // Skip for regular users
            case COMPLETED -> true;
        };
    }

    public RegistrationStep getNextStep() {
        return switch (currentStep) {
            case PHONE_VERIFICATION -> RegistrationStep.EMAIL_VERIFICATION;
            case EMAIL_VERIFICATION -> RegistrationStep.IDENTITY_VERIFICATION;
            case IDENTITY_VERIFICATION -> RegistrationStep.TRANSACTION_PIN;
            case TRANSACTION_PIN -> intendedRole == UserRole.AGENT ? 
                RegistrationStep.BUSINESS_VERIFICATION : RegistrationStep.COMPLETED;
            case BUSINESS_VERIFICATION -> RegistrationStep.COMPLETED;
            case COMPLETED -> RegistrationStep.COMPLETED;
        };
    }

    public boolean isReadyForCompletion() {
        return phoneVerificationStatus == VerificationStatus.VERIFIED &&
               emailVerificationStatus == VerificationStatus.VERIFIED &&
               (idVerificationStatus == VerificationStatus.VERIFIED ||
                idVerificationStatus == VerificationStatus.UNDER_REVIEW) &&
               pinSet;
    }

    public String getFullName() {
        return firstName + " " + lastName;
    }

    public boolean isEthiopian() {
        return "ET".equals(countryCode);
    }

    public boolean isRwandan() {
        return "RW".equals(countryCode);
    }

    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getPlatform() { return platform; }
    public void setPlatform(String platform) { this.platform = platform; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public LocalDate getDateOfBirth() { return dateOfBirth; }
    public void setDateOfBirth(LocalDate dateOfBirth) { this.dateOfBirth = dateOfBirth; }

    public String getPasswordHash() { return passwordHash; }
    public void setPasswordHash(String passwordHash) { this.passwordHash = passwordHash; }

    public String getCountryCode() { return countryCode; }
    public void setCountryCode(String countryCode) { this.countryCode = countryCode; }

    public UserRole getIntendedRole() { return intendedRole; }
    public void setIntendedRole(UserRole intendedRole) { this.intendedRole = intendedRole; }

    public RegistrationStep getCurrentStep() { return currentStep; }
    public void setCurrentStep(RegistrationStep currentStep) { this.currentStep = currentStep; }

    public VerificationStatus getPhoneVerificationStatus() { return phoneVerificationStatus; }
    public void setPhoneVerificationStatus(VerificationStatus phoneVerificationStatus) { 
        this.phoneVerificationStatus = phoneVerificationStatus; 
    }

    public VerificationStatus getEmailVerificationStatus() { return emailVerificationStatus; }
    public void setEmailVerificationStatus(VerificationStatus emailVerificationStatus) { 
        this.emailVerificationStatus = emailVerificationStatus; 
    }

    public LocalDateTime getPhoneVerifiedAt() { return phoneVerifiedAt; }
    public void setPhoneVerifiedAt(LocalDateTime phoneVerifiedAt) { this.phoneVerifiedAt = phoneVerifiedAt; }

    public LocalDateTime getEmailVerifiedAt() { return emailVerifiedAt; }
    public void setEmailVerifiedAt(LocalDateTime emailVerifiedAt) { this.emailVerifiedAt = emailVerifiedAt; }

    public String getIdType() { return idType; }
    public void setIdType(String idType) { this.idType = idType; }

    public String getIdNumberHash() { return idNumberHash; }
    public void setIdNumberHash(String idNumberHash) { this.idNumberHash = idNumberHash; }

    public String getIdImagePath() { return idImagePath; }
    public void setIdImagePath(String idImagePath) { this.idImagePath = idImagePath; }

    public VerificationStatus getIdVerificationStatus() { return idVerificationStatus; }
    public void setIdVerificationStatus(VerificationStatus idVerificationStatus) { 
        this.idVerificationStatus = idVerificationStatus; 
    }

    public LocalDateTime getIdVerifiedAt() { return idVerifiedAt; }
    public void setIdVerifiedAt(LocalDateTime idVerifiedAt) { this.idVerifiedAt = idVerifiedAt; }

    public String getTransactionPinHash() { return transactionPinHash; }
    public void setTransactionPinHash(String transactionPinHash) { this.transactionPinHash = transactionPinHash; }

    public Boolean getPinSet() { return pinSet; }
    public void setPinSet(Boolean pinSet) { this.pinSet = pinSet; }

    public Boolean getBiometricEnabled() { return biometricEnabled; }
    public void setBiometricEnabled(Boolean biometricEnabled) { this.biometricEnabled = biometricEnabled; }

    public String getBiometricData() { return biometricData; }
    public void setBiometricData(String biometricData) { this.biometricData = biometricData; }

    public String getRegistrationIpAddress() { return registrationIpAddress; }
    public void setRegistrationIpAddress(String registrationIpAddress) { this.registrationIpAddress = registrationIpAddress; }

    public String getDeviceInfo() { return deviceInfo; }
    public void setDeviceInfo(String deviceInfo) { this.deviceInfo = deviceInfo; }

    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }

    public String getReferralCode() { return referralCode; }
    public void setReferralCode(String referralCode) { this.referralCode = referralCode; }

    public Boolean getIsCompleted() { return isCompleted; }
    public void setIsCompleted(Boolean isCompleted) { this.isCompleted = isCompleted; }

    public UUID getCompletedUserId() { return completedUserId; }
    public void setCompletedUserId(UUID completedUserId) { this.completedUserId = completedUserId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
