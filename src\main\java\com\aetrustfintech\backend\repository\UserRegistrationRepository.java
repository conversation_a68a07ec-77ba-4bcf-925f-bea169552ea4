package com.aetrustfintech.backend.repository;

import com.aetrustfintech.backend.enums.RegistrationStep;
import com.aetrustfintech.backend.enums.VerificationStatus;
import com.aetrustfintech.backend.model.UserRegistration;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface UserRegistrationRepository extends JpaRepository<UserRegistration, Long> {

    // Find by unique identifiers
    Optional<UserRegistration> findByEmail(String email);
    Optional<UserRegistration> findByPhone(String phone);
    Optional<UserRegistration> findByEmailOrPhone(String email, String phone);

    // Find by registration status
    List<UserRegistration> findByIsCompleted(Boolean isCompleted);
    List<UserRegistration> findByCurrentStep(RegistrationStep currentStep);
    
    @Query("SELECT ur FROM UserRegistration ur WHERE ur.isCompleted = false AND ur.currentStep = :step")
    Page<UserRegistration> findIncompleteRegistrationsByStep(@Param("step") RegistrationStep step, Pageable pageable);

    // Find by verification statuses
    List<UserRegistration> findByPhoneVerificationStatus(VerificationStatus status);
    List<UserRegistration> findByEmailVerificationStatus(VerificationStatus status);
    List<UserRegistration> findByIdVerificationStatus(VerificationStatus status);

    // Find by country
    List<UserRegistration> findByCountryCode(String countryCode);
    
    @Query("SELECT ur FROM UserRegistration ur WHERE ur.countryCode = :countryCode AND ur.isCompleted = false")
    List<UserRegistration> findIncompleteRegistrationsByCountry(@Param("countryCode") String countryCode);

    // Find by platform
    List<UserRegistration> findByPlatform(String platform);

    // Find by referral code
    List<UserRegistration> findByReferralCode(String referralCode);

    // Find stale registrations (older than specified time and not completed)
    @Query("SELECT ur FROM UserRegistration ur WHERE ur.isCompleted = false AND ur.createdAt < :cutoffTime")
    List<UserRegistration> findStaleRegistrations(@Param("cutoffTime") LocalDateTime cutoffTime);

    // Find registrations that need cleanup (very old and incomplete)
    @Query("SELECT ur FROM UserRegistration ur WHERE ur.isCompleted = false AND ur.createdAt < :cutoffTime AND ur.currentStep = 'PHONE_VERIFICATION'")
    List<UserRegistration> findRegistrationsForCleanup(@Param("cutoffTime") LocalDateTime cutoffTime);

    // Count registrations by status
    @Query("SELECT COUNT(ur) FROM UserRegistration ur WHERE ur.isCompleted = :isCompleted")
    Long countByCompletionStatus(@Param("isCompleted") Boolean isCompleted);

    @Query("SELECT COUNT(ur) FROM UserRegistration ur WHERE ur.currentStep = :step")
    Long countByCurrentStep(@Param("step") RegistrationStep step);

    @Query("SELECT COUNT(ur) FROM UserRegistration ur WHERE ur.countryCode = :countryCode AND ur.isCompleted = true")
    Long countCompletedRegistrationsByCountry(@Param("countryCode") String countryCode);

    // Analytics queries
    @Query("SELECT ur.countryCode, COUNT(ur) FROM UserRegistration ur WHERE ur.isCompleted = true GROUP BY ur.countryCode")
    List<Object[]> getRegistrationCountsByCountry();

    @Query("SELECT ur.platform, COUNT(ur) FROM UserRegistration ur WHERE ur.isCompleted = true GROUP BY ur.platform")
    List<Object[]> getRegistrationCountsByPlatform();

    @Query("SELECT ur.currentStep, COUNT(ur) FROM UserRegistration ur WHERE ur.isCompleted = false GROUP BY ur.currentStep")
    List<Object[]> getIncompleteRegistrationsByStep();

    // Date range queries
    @Query("SELECT ur FROM UserRegistration ur WHERE ur.createdAt BETWEEN :startDate AND :endDate")
    List<UserRegistration> findRegistrationsByDateRange(@Param("startDate") LocalDateTime startDate, 
                                                       @Param("endDate") LocalDateTime endDate);

    @Query("SELECT COUNT(ur) FROM UserRegistration ur WHERE ur.createdAt BETWEEN :startDate AND :endDate AND ur.isCompleted = true")
    Long countCompletedRegistrationsByDateRange(@Param("startDate") LocalDateTime startDate, 
                                              @Param("endDate") LocalDateTime endDate);

    // Update operations
    @Modifying
    @Query("UPDATE UserRegistration ur SET ur.currentStep = :nextStep WHERE ur.id = :id")
    int updateCurrentStep(@Param("id") Long id, @Param("nextStep") RegistrationStep nextStep);

    @Modifying
    @Query("UPDATE UserRegistration ur SET ur.phoneVerificationStatus = :status, ur.phoneVerifiedAt = :verifiedAt WHERE ur.id = :id")
    int updatePhoneVerificationStatus(@Param("id") Long id, @Param("status") VerificationStatus status, 
                                    @Param("verifiedAt") LocalDateTime verifiedAt);

    @Modifying
    @Query("UPDATE UserRegistration ur SET ur.emailVerificationStatus = :status, ur.emailVerifiedAt = :verifiedAt WHERE ur.id = :id")
    int updateEmailVerificationStatus(@Param("id") Long id, @Param("status") VerificationStatus status, 
                                    @Param("verifiedAt") LocalDateTime verifiedAt);

    @Modifying
    @Query("UPDATE UserRegistration ur SET ur.idVerificationStatus = :status, ur.idVerifiedAt = :verifiedAt WHERE ur.id = :id")
    int updateIdVerificationStatus(@Param("id") Long id, @Param("status") VerificationStatus status, 
                                 @Param("verifiedAt") LocalDateTime verifiedAt);

    @Modifying
    @Query("UPDATE UserRegistration ur SET ur.isCompleted = true, ur.completedUserId = :userId WHERE ur.id = :id")
    int markAsCompleted(@Param("id") Long id, @Param("userId") Long userId);

    // Cleanup operations
    @Modifying
    @Query("DELETE FROM UserRegistration ur WHERE ur.isCompleted = false AND ur.createdAt < :cutoffTime")
    int deleteStaleRegistrations(@Param("cutoffTime") LocalDateTime cutoffTime);

    // Security queries
    @Query("SELECT ur FROM UserRegistration ur WHERE ur.registrationIpAddress = :ipAddress AND ur.createdAt > :since")
    List<UserRegistration> findRecentRegistrationsByIpAddress(@Param("ipAddress") String ipAddress, 
                                                             @Param("since") LocalDateTime since);

    @Query("SELECT COUNT(ur) FROM UserRegistration ur WHERE ur.email = :email AND ur.createdAt > :since")
    Long countRecentRegistrationAttemptsByEmail(@Param("email") String email, @Param("since") LocalDateTime since);

    @Query("SELECT COUNT(ur) FROM UserRegistration ur WHERE ur.phone = :phone AND ur.createdAt > :since")
    Long countRecentRegistrationAttemptsByPhone(@Param("phone") String phone, @Param("since") LocalDateTime since);

    // Exists checks
    boolean existsByEmail(String email);
    boolean existsByPhone(String phone);
    boolean existsByEmailAndIsCompletedFalse(String email);
    boolean existsByPhoneAndIsCompletedFalse(String phone);
}
