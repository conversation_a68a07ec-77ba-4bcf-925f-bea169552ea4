package com.aetrustfintech.backend.repository;

import com.aetrustfintech.backend.enums.AccountStatus;
import com.aetrustfintech.backend.enums.UserRole;
import com.aetrustfintech.backend.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    Optional<User> findByEmail(String email);
    
    Optional<User> findByPhone(String phone);
    
    Optional<User> findByUsername(String username);
    
    @Query("SELECT u FROM User u WHERE u.email = :identifier OR u.phone = :identifier OR u.username = :identifier")
    Optional<User> findByIdentifier(@Param("identifier") String identifier);
    
    boolean existsByEmail(String email);
    
    boolean existsByPhone(String phone);
    
    boolean existsByUsername(String username);
    
    List<User> findByRole(UserRole role);
    
    List<User> findByAccountStatus(AccountStatus accountStatus);
    
    @Query("SELECT u FROM User u WHERE u.accountStatus = :status AND u.createdAt >= :since")
    List<User> findByAccountStatusAndCreatedAtAfter(@Param("status") AccountStatus status, @Param("since") LocalDateTime since);
    
    @Query("SELECT u FROM User u WHERE u.isVerified = true AND u.accountStatus = 'ACTIVE'")
    Page<User> findActiveVerifiedUsers(Pageable pageable);
    
    @Query("SELECT u FROM User u WHERE u.lastLogin < :cutoffDate AND u.accountStatus = 'ACTIVE'")
    List<User> findInactiveUsers(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    @Query("SELECT COUNT(u) FROM User u WHERE u.createdAt >= :startDate AND u.createdAt <= :endDate")
    Long countUsersByDateRange(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT u FROM User u WHERE " +
           "(LOWER(u.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(u.phone) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<User> searchUsers(@Param("searchTerm") String searchTerm, Pageable pageable);
}
