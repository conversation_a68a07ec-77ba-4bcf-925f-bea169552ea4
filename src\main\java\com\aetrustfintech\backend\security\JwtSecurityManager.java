package com.aetrustfintech.backend.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import io.jsonwebtoken.security.SecurityException;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.crypto.SecretKey;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;


@Component
public class JwtSecurityManager extends OncePerRequestFilter implements AuthenticationEntryPoint {

    private static final Logger logger = LoggerFactory.getLogger(JwtSecurityManager.class);
    
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    @Value("${jwt.expiration}")
    private Long jwtExpiration;
    
    @Value("${jwt.refresh-expiration}")
    private Long refreshExpiration;
    
    private static final String SECURITY_HEADER_X_CONTENT_TYPE = "X-Content-Type-Options";
    private static final String SECURITY_HEADER_X_FRAME = "X-Frame-Options";
    private static final String SECURITY_HEADER_X_XSS = "X-XSS-Protection";
    private static final String SECURITY_HEADER_REFERRER = "Referrer-Policy";
    private static final String SECURITY_HEADER_CSP = "Content-Security-Policy";
    
    private final Map<String, List<Long>> requestTracker = new HashMap<>();
    private static final int MAX_REQUESTS_PER_MINUTE = 60;
    private static final long RATE_LIMIT_WINDOW = 60000; // 1 minute
    
    
    private SecretKey getSigningKey() {
        return Keys.hmacShaKeyFor(jwtSecret.getBytes());
    }
    
    public String generateToken(UserDetails userDetails) {
        Map<String, Object> claims = new HashMap<>();
        return createToken(claims, userDetails.getUsername(), jwtExpiration);
    }
    
    public String generateToken(String username, Map<String, Object> extraClaims) {
        return createToken(extraClaims, username, jwtExpiration);
    }
    
    public String generateRefreshToken(String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("type", "refresh");
        return createToken(claims, username, refreshExpiration);
    }
    
    private String createToken(Map<String, Object> claims, String subject, Long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration);
        
        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }
    
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }
    
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }
    
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }
    
    private Claims extractAllClaims(String token) {
        try {
            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(token)
                    .getPayload();
        } catch (ExpiredJwtException e) {
            logger.warn("JWT token is expired: {}", e.getMessage());
            throw e;
        } catch (UnsupportedJwtException e) {
            logger.error("JWT token is unsupported: {}", e.getMessage());
            throw e;
        } catch (MalformedJwtException e) {
            logger.error("Invalid JWT token: {}", e.getMessage());
            throw e;
        } catch (SecurityException e) {
            logger.error("Invalid JWT signature: {}", e.getMessage());
            throw e;
        } catch (IllegalArgumentException e) {
            logger.error("JWT claims string is empty: {}", e.getMessage());
            throw e;
        }
    }
    
    public Boolean isTokenExpired(String token) {
        try {
            return extractExpiration(token).before(new Date());
        } catch (ExpiredJwtException e) {
            return true;
        }
    }
    
    public Boolean validateToken(String token, UserDetails userDetails) {
        try {
            final String username = extractUsername(token);
            return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
        } catch (JwtException e) {
            logger.error("JWT validation failed: {}", e.getMessage());
            return false;
        }
    }
    
    public Boolean validateToken(String token) {
        try {
            extractAllClaims(token);
            return !isTokenExpired(token);
        } catch (JwtException e) {
            logger.error("JWT validation failed: {}", e.getMessage());
            return false;
        }
    }
    
    public String getTokenType(String token) {
        try {
            Claims claims = extractAllClaims(token);
            return (String) claims.get("type");
        } catch (JwtException e) {
            return null;
        }
    }
    
    public Long getExpirationTime() {
        return jwtExpiration;
    }
    
    public Long getRefreshExpirationTime() {
        return refreshExpiration;
    }
    
    private boolean isRateLimited(String clientId) {
        long currentTime = System.currentTimeMillis();
        List<Long> requests = requestTracker.computeIfAbsent(clientId, k -> new ArrayList<>());
        
        requests.removeIf(timestamp -> currentTime - timestamp > RATE_LIMIT_WINDOW);
        
        if (requests.size() >= MAX_REQUESTS_PER_MINUTE) {
            logger.warn("Rate limit exceeded for client: {}", clientId);
            return true;
        }
        
        requests.add(currentTime);
        return false;
    }
    
    private String getClientIdentifier(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        return request.getRemoteAddr();
    }
    

private void addSecurityHeaders(HttpServletResponse response) {
        response.setHeader(SECURITY_HEADER_X_CONTENT_TYPE, "nosniff");
        response.setHeader(SECURITY_HEADER_X_FRAME, "DENY");
        response.setHeader(SECURITY_HEADER_X_XSS, "1; mode=block");
        response.setHeader(SECURITY_HEADER_REFERRER, "strict-origin-when-cross-origin");
        response.setHeader(SECURITY_HEADER_CSP, "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'");
        response.setHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        addSecurityHeaders(response);

        String clientId = getClientIdentifier(request);
        if (isRateLimited(clientId)) {
            sendRateLimitResponse(response);
            return;
        }

        final String requestTokenHeader = request.getHeader("Authorization");

        String username = null;
        String jwtToken = null;

        if (requestTokenHeader != null && requestTokenHeader.startsWith("Bearer ")) {
            jwtToken = requestTokenHeader.substring(7);
            try {
                username = extractUsername(jwtToken);
            } catch (IllegalArgumentException e) {
                logger.error("Unable to get JWT Token");
            } catch (ExpiredJwtException e) {
                logger.warn("JWT Token has expired");
            } catch (MalformedJwtException e) {
                logger.error("Invalid JWT Token");
            } catch (SecurityException e) {
                logger.error("Invalid JWT signature");
            }
        } else {
            logger.debug("JWT Token does not begin with Bearer String");
        }

        if (username != null && SecurityContextHolder.getContext().getAuthentication() == null) {
            try {
                if (validateToken(jwtToken)) {
                    String userIdStr = extractClaim(jwtToken, claims -> claims.get("userId", String.class));
                    UUID userId = userIdStr != null ? UUID.fromString(userIdStr) : null;
                    String role = extractClaim(jwtToken, claims -> claims.get("role", String.class));
                    String kycLevel = extractClaim(jwtToken, claims -> claims.get("kycLevel", String.class));

                    List<SimpleGrantedAuthority> authorities = List.of(
                        new SimpleGrantedAuthority("ROLE_" + role)
                    );

                    UsernamePasswordAuthenticationToken authToken =
                        new UsernamePasswordAuthenticationToken(username, null, authorities);
                    authToken.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    SecurityContextHolder.getContext().setAuthentication(authToken);

                    request.setAttribute("userId", userId);
                    request.setAttribute("userRole", role);
                    request.setAttribute("kycLevel", kycLevel);
                    request.setAttribute("username", username);

                    logger.debug("JWT authentication successful for user: {}", username);
                }
            } catch (Exception e) {
                logger.error("JWT authentication failed", e);
            }
        }

        filterChain.doFilter(request, response);
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        String path = request.getRequestURI();

        return path.startsWith("/api/v1/auth/") ||
               path.startsWith("/api/v1/registration/") ||
               path.startsWith("/api/v1/health/") ||
               path.startsWith("/swagger-ui/") ||
               path.startsWith("/v3/api-docs/") ||
               path.startsWith("/actuator/health") ||
               path.equals("/api-docs") ||
               path.equals("/swagger-ui.html");
    }


    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {

        logger.warn("Unauthorized access attempt: {}", authException.getMessage());

        response.setContentType("application/json");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", "Unauthorized access - Please provide a valid authentication token");
        errorResponse.put("timestamp", LocalDateTime.now().toString());
        errorResponse.put("path", request.getRequestURI());

        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getOutputStream(), errorResponse);
    }


    private void sendRateLimitResponse(HttpServletResponse response) throws IOException {
        response.setContentType("application/json");
        response.setStatus(429); // Too Many Requests

        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", "Rate limit exceeded. Please try again later.");
        errorResponse.put("timestamp", LocalDateTime.now().toString());
        errorResponse.put("retryAfter", RATE_LIMIT_WINDOW / 1000);

        ObjectMapper mapper = new ObjectMapper();
        mapper.writeValue(response.getOutputStream(), errorResponse);
    }
}
