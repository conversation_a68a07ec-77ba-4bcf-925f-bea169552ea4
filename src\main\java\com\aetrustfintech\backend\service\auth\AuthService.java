package com.aetrustfintech.backend.service.auth;

import com.aetrustfintech.backend.dto.auth.*;
import com.aetrustfintech.backend.dto.user.UserResponse;
import com.aetrustfintech.backend.enums.AccountStatus;
import com.aetrustfintech.backend.enums.KycLevel;
import com.aetrustfintech.backend.exception.AuthenticationException;
import com.aetrustfintech.backend.exception.ValidationException;
import com.aetrustfintech.backend.service.initialization.CountryInitializationService;
import com.aetrustfintech.backend.model.RefreshToken;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.RefreshTokenRepository;
import com.aetrustfintech.backend.repository.UserRepository;
import com.aetrustfintech.backend.security.JwtSecurityManager;
import com.aetrustfintech.backend.service.user.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@Service
@Transactional
public class AuthService {

    private static final Logger logger = LoggerFactory.getLogger(AuthService.class);
    private static final int MAX_LOGIN_ATTEMPTS = 5;
    private static final int LOCKOUT_DURATION_MINUTES = 30;

    private final UserRepository userRepository;
    private final RefreshTokenRepository refreshTokenRepository;
    private final PasswordEncoder passwordEncoder;
    private final JwtSecurityManager jwtSecurityManager;
    private final CountryInitializationService countryInitService;
    private final OtpService otpService;
    private final UserService userService;

    @Autowired
    public AuthService(UserRepository userRepository,
                      RefreshTokenRepository refreshTokenRepository,
                      PasswordEncoder passwordEncoder,
                      JwtSecurityManager jwtSecurityManager,
                      CountryInitializationService countryInitService,
                      OtpService otpService,
                      UserService userService) {
        this.userRepository = userRepository;
        this.refreshTokenRepository = refreshTokenRepository;
        this.passwordEncoder = passwordEncoder;
        this.jwtSecurityManager = jwtSecurityManager;
        this.countryInitService = countryInitService;
        this.otpService = otpService;
        this.userService = userService;
    }

    public AuthResponse register(RegisterRequest request) {
        logger.info("Registering new user with email: {}", request.getEmail());

        if (userRepository.existsByEmail(request.getEmail())) {
            throw new ValidationException("Email already registered");
        }

        if (userRepository.existsByPhone(request.getPhone())) {
            throw new ValidationException("Phone number already registered");
        }

        User user = new User();
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setFirstName(request.getFirstName());
        user.setLastName(request.getLastName());
        user.setPassword(passwordEncoder.encode(request.getPassword()));
        user.setCountryCode(request.getCountryCode());
        user.setLanguagePreference(request.getLanguagePreference());
        user.setAccountStatus(AccountStatus.ACTIVE);

        user = userRepository.save(user);

        otpService.sendOtp(request.getEmail(), "REGISTRATION");
        otpService.sendOtp(request.getPhone(), "REGISTRATION");

        logger.info("User registered successfully with ID: {}", user.getId());

        return AuthResponse.requiresOtp(
            "Registration successful. Please verify your email and phone number.",
            request.getEmail()
        );
    }

    public AuthResponse login(LoginRequest request) {
        logger.info("Login attempt for identifier: {}", request.getIdentifier());

        Optional<User> userOpt = userRepository.findByIdentifier(request.getIdentifier());
        if (userOpt.isEmpty()) {
            throw new AuthenticationException("Invalid credentials");
        }

        User user = userOpt.get();

        if (user.isAccountLocked()) {
            throw new AuthenticationException("Account is temporarily locked. Please try again later.");
        }

        if (user.getAccountStatus() != AccountStatus.ACTIVE) {
            throw new AuthenticationException("Account is not active");
        }

        if (!passwordEncoder.matches(request.getPassword(), user.getPassword())) {
            handleFailedLogin(user);
            throw new AuthenticationException("Invalid credentials");
        }

        user.setLoginAttempts(0);
        user.setAccountLockedUntil(null);
        user.setLastLogin(LocalDateTime.now());
        userRepository.save(user);

        if (!user.isEmailVerified() || !user.isPhoneVerified()) {
            String identifier = !user.isEmailVerified() ? user.getEmail() : user.getPhone();
            otpService.sendOtp(identifier, "LOGIN");
            
            return AuthResponse.requiresOtp(
                "Please verify your " + (!user.isEmailVerified() ? "email" : "phone number"),
                identifier
            );
        }

        return generateAuthResponse(user, request.getDeviceInfo(), request.getIpAddress());
    }

    private void handleFailedLogin(User user) {
        user.setLoginAttempts(user.getLoginAttempts() + 1);
        
        if (user.getLoginAttempts() >= MAX_LOGIN_ATTEMPTS) {
            user.setAccountLockedUntil(LocalDateTime.now().plusMinutes(LOCKOUT_DURATION_MINUTES));
            logger.warn("Account locked for user ID: {} due to too many failed login attempts", user.getId());
        }
        
        userRepository.save(user);
    }

    private AuthResponse generateAuthResponse(User user, String deviceInfo, String ipAddress) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId().toString()); 
        claims.put("role", user.getRole().name());
        claims.put("kycLevel", user.getKycLevel().name());

        String accessToken = jwtSecurityManager.generateToken(user.getEmail(), claims);
        String refreshToken = jwtSecurityManager.generateRefreshToken(user.getEmail());

        saveRefreshToken(user, refreshToken, deviceInfo, ipAddress);

        UserResponse userResponse = userService.convertToResponse(user);

        logger.info("Login successful for user ID: {}", user.getId());

        return AuthResponse.success(
            "Login successful",
            accessToken,
            refreshToken,
            jwtSecurityManager.getExpirationTime(),
            userResponse
        );
    }

    private void saveRefreshToken(User user, String token, String deviceInfo, String ipAddress) {
        RefreshToken refreshToken = new RefreshToken();
        refreshToken.setToken(token);
        refreshToken.setUser(user);
        refreshToken.setExpiresAt(LocalDateTime.now().plusSeconds(jwtSecurityManager.getRefreshExpirationTime() / 1000));
        refreshToken.setDeviceInfo(deviceInfo);
        refreshToken.setIpAddress(ipAddress);
        
        refreshTokenRepository.save(refreshToken);
    }

    public AuthResponse refreshToken(String refreshToken) {
        logger.info("Refreshing token");

        Optional<RefreshToken> tokenOpt = refreshTokenRepository.findByToken(refreshToken);
        if (tokenOpt.isEmpty() || !tokenOpt.get().isValid()) {
            throw new AuthenticationException("Invalid refresh token");
        }

        RefreshToken token = tokenOpt.get();
        User user = token.getUser();

        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", user.getId().toString()); 
        claims.put("role", user.getRole().name());
        claims.put("kycLevel", user.getKycLevel().name());

        String newAccessToken = jwtSecurityManager.generateToken(user.getEmail(), claims);
        UserResponse userResponse = userService.convertToResponse(user);

        return AuthResponse.success(
            "Token refreshed successfully",
            newAccessToken,
            refreshToken,
            jwtSecurityManager.getExpirationTime(),
            userResponse
        );
    }

    public void logout(String refreshToken) {
        logger.info("Logging out user");

        Optional<RefreshToken> tokenOpt = refreshTokenRepository.findByToken(refreshToken);
        if (tokenOpt.isPresent()) {
            RefreshToken token = tokenOpt.get();
            token.setIsRevoked(true);
            refreshTokenRepository.save(token);
        }
    }

    public void logoutAllDevices(UUID userId) {
        logger.info("Logging out all devices for user ID: {}", userId);
        refreshTokenRepository.revokeAllUserTokens(userId);
    }
}
