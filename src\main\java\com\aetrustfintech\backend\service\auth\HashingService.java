package com.aetrustfintech.backend.service.auth;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

@Service
public class HashingService {

    private static final Logger logger = LoggerFactory.getLogger(HashingService.class);
    
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;
    
    private final PasswordEncoder passwordEncoder;
    private SecretKey encryptionKey;
    private final SecureRandom secureRandom;

    @Value("${security.encryption.key:default-encryption-key-change-in-production}")
    private String encryptionKeyString;

    public HashingService() {
        this.passwordEncoder = new BCryptPasswordEncoder(12);
        this.secureRandom = new SecureRandom();
    }

    @PostConstruct
    public void init() {
        this.encryptionKey = generateEncryptionKey();
    }

    private SecretKey generateEncryptionKey() {
        try {
            // In production, this should come from a secure key management system
            byte[] keyBytes = encryptionKeyString.getBytes(StandardCharsets.UTF_8);
            MessageDigest sha = MessageDigest.getInstance("SHA-256");
            keyBytes = sha.digest(keyBytes);
            return new SecretKeySpec(keyBytes, AES_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error generating encryption key", e);
            throw new RuntimeException("Failed to generate encryption key", e);
        }
    }

    /**
     *  (irreversible)
     */
    public String hashPassword(String password) {
        if (password == null || password.isEmpty()) {
            throw new IllegalArgumentException("Password cannot be null or empty");
        }
        return passwordEncoder.encode(password);
    }

    /**
     * Verify password against hash
     */
    public boolean verifyPassword(String password, String hash) {
        if (password == null || hash == null) {
            return false;
        }
        return passwordEncoder.matches(password, hash);
    }



    public String hashPiiData(String data) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("Data cannot be null or empty");
        }
        
        try {
            byte[] salt = new byte[16];
            secureRandom.nextBytes(salt);
            
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(salt);
            byte[] hashedBytes = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            
            byte[] combined = new byte[salt.length + hashedBytes.length];
            System.arraycopy(salt, 0, combined, 0, salt.length);
            System.arraycopy(hashedBytes, 0, combined, salt.length, hashedBytes.length);
            
            return Base64.getEncoder().encodeToString(combined);
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error hashing PII data", e);
            throw new RuntimeException("Failed to hash PII data", e);
        }
    }

  
    public boolean verifyPiiData(String data, String hash) {
        if (data == null || hash == null) {
            return false;
        }
        
        try {
            byte[] combined = Base64.getDecoder().decode(hash);
            
            byte[] salt = new byte[16];
            System.arraycopy(combined, 0, salt, 0, 16);
            
            byte[] originalHash = new byte[combined.length - 16];
            System.arraycopy(combined, 16, originalHash, 0, originalHash.length);
            
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            digest.update(salt);
            byte[] newHash = digest.digest(data.getBytes(StandardCharsets.UTF_8));
            
            return MessageDigest.isEqual(originalHash, newHash);
        } catch (Exception e) {
            logger.error("Error verifying PII data", e);
            return false;
        }
    }

    // reversible
    public String encryptSensitiveData(String data) {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("Data cannot be null or empty");
        }
        
        try {
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            
            byte[] iv = new byte[GCM_IV_LENGTH];
            secureRandom.nextBytes(iv);
            
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, encryptionKey, parameterSpec);
            
            byte[] encryptedData = cipher.doFinal(data.getBytes(StandardCharsets.UTF_8));
            
            byte[] combined = new byte[iv.length + encryptedData.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encryptedData, 0, combined, iv.length, encryptedData.length);
            
            return Base64.getEncoder().encodeToString(combined);
        } catch (Exception e) {
            logger.error("Error encrypting sensitive data", e);
            throw new RuntimeException("Failed to encrypt sensitive data", e);
        }
    }


    public String decryptSensitiveData(String encryptedData) {
        if (encryptedData == null || encryptedData.isEmpty()) {
            throw new IllegalArgumentException("Encrypted data cannot be null or empty");
        }
        
        try {
            byte[] combined = Base64.getDecoder().decode(encryptedData);
            
            byte[] iv = new byte[GCM_IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, GCM_IV_LENGTH);
            
            byte[] encrypted = new byte[combined.length - GCM_IV_LENGTH];
            System.arraycopy(combined, GCM_IV_LENGTH, encrypted, 0, encrypted.length);
            
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            GCMParameterSpec parameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, encryptionKey, parameterSpec);
            
            byte[] decryptedData = cipher.doFinal(encrypted);
            return new String(decryptedData, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("Error decrypting sensitive data", e);
            throw new RuntimeException("Failed to decrypt sensitive data", e);
        }
    }

    /**
     *  (irreversible)
     */
    public String hashOtp(String otp) {
        if (otp == null || otp.isEmpty()) {
            throw new IllegalArgumentException("OTP cannot be null or empty");
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashedBytes = digest.digest(otp.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashedBytes);
        } catch (NoSuchAlgorithmException e) {
            logger.error("Error hashing OTP", e);
            throw new RuntimeException("Failed to hash OTP", e);
        }
    }

    
    public boolean verifyOtp(String otp, String hash) {
        if (otp == null || hash == null) {
            return false;
        }
        
        String otpHash = hashOtp(otp);
        return MessageDigest.isEqual(otpHash.getBytes(), hash.getBytes());
    }

    
    public String generateSecureToken(int length) {
        byte[] tokenBytes = new byte[length];
        secureRandom.nextBytes(tokenBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(tokenBytes);
    }

     /**
     *  (irreversible)
     */
  
    public String hashTransactionPin(String pin) {
        if (pin == null || pin.isEmpty()) {
            throw new IllegalArgumentException("PIN cannot be null or empty");
        }
        return passwordEncoder.encode(pin);
    }

   
    public boolean verifyTransactionPin(String pin, String hash) {
        if (pin == null || hash == null) {
            return false;
        }
        return passwordEncoder.matches(pin, hash);
    }

    
    public String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        
        int visibleChars = Math.min(2, data.length() / 4);
        String start = data.substring(0, visibleChars);
        String end = data.substring(data.length() - visibleChars);
        String middle = "*".repeat(data.length() - (2 * visibleChars));
        
        return start + middle + end;
    }

   
    public String maskEmail(String email) {
        if (email == null || !email.contains("@")) {
            return "****@****.***";
        }
        
        String[] parts = email.split("@");
        String localPart = parts[0];
        String domainPart = parts[1];
        
        String maskedLocal = localPart.length() > 2 ? 
            localPart.substring(0, 2) + "****" : "****";
        String maskedDomain = domainPart.length() > 4 ? 
            "****" + domainPart.substring(domainPart.length() - 4) : "****";
        
        return maskedLocal + "@" + maskedDomain;
    }


    public String maskPhoneNumber(String phone) {
        if (phone == null || phone.length() <= 4) {
            return "****";
        }
        
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 2);
    }
}
