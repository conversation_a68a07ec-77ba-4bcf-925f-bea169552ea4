package com.aetrustfintech.backend.service.auth;

import com.aetrustfintech.backend.dto.auth.OtpRequest;
import com.aetrustfintech.backend.enums.VerificationStatus;
import com.aetrustfintech.backend.exception.ValidationException;
import com.aetrustfintech.backend.model.OtpVerification;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.OtpVerificationRepository;
import com.aetrustfintech.backend.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Random;

@Service
@Transactional
public class OtpService {

    private static final Logger logger = LoggerFactory.getLogger(OtpService.class);
    private static final int MAX_OTP_REQUESTS_PER_HOUR = 5;

    @Value("${otp.expiration:300000}") // 5 minutes default
    private Long otpExpiration;

    @Value("${otp.length:6}")
    private Integer otpLength;

    private final OtpVerificationRepository otpRepository;
    private final UserRepository userRepository;
    private final com.aetrustfintech.backend.service.notification.EmailService emailService;
    private final com.aetrustfintech.backend.service.notification.SmsService smsService;

    @Autowired
    public OtpService(OtpVerificationRepository otpRepository,
                     UserRepository userRepository,
                     com.aetrustfintech.backend.service.notification.EmailService emailService,
                     com.aetrustfintech.backend.service.notification.SmsService smsService) {
        this.otpRepository = otpRepository;
        this.userRepository = userRepository;
        this.emailService = emailService;
        this.smsService = smsService;
    }

    public void sendOtp(String identifier, String otpType) {
        logger.info("Sending OTP to identifier: {} for type: {}", identifier, otpType);

        // Check rate limiting
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        Long recentRequests = otpRepository.countOtpRequestsSince(identifier, oneHourAgo);
        
        if (recentRequests >= MAX_OTP_REQUESTS_PER_HOUR) {
            throw new ValidationException("Too many OTP requests. Please try again later.");
        }

        Optional<OtpVerification> existingOtp = otpRepository.findLatestPendingOtp(identifier, otpType);
        if (existingOtp.isPresent()) {
            OtpVerification otp = existingOtp.get();
            otp.setStatus(VerificationStatus.EXPIRED);
            otpRepository.save(otp);
        }

        String otpCode = generateOtpCode();
        
        OtpVerification otpVerification = new OtpVerification();
        otpVerification.setIdentifier(identifier);
        otpVerification.setOtpCode(otpCode);
        otpVerification.setOtpType(otpType);
        otpVerification.setExpiresAt(LocalDateTime.now().plusSeconds(otpExpiration / 1000));
        
        Optional<User> userOpt = userRepository.findByIdentifier(identifier);
        userOpt.ifPresent(otpVerification::setUser);
        
        otpRepository.save(otpVerification);

        if (isEmail(identifier)) {
            emailService.sendOtpEmail(identifier, otpCode, otpType);
        } else {
            smsService.sendOtpSms(identifier, otpCode, otpType);
        }

        logger.info("OTP sent successfully to: {}", identifier);
    }

    public boolean verifyOtp(OtpRequest request) {
        logger.info("Verifying OTP for identifier: {} and type: {}", request.getIdentifier(), request.getOtpType());

        Optional<OtpVerification> otpOpt = otpRepository.findLatestPendingOtp(
            request.getIdentifier(), 
            request.getOtpType()
        );

        if (otpOpt.isEmpty()) {
            logger.warn("No pending OTP found for identifier: {}", request.getIdentifier());
            return false;
        }

        OtpVerification otp = otpOpt.get();

        if (!otp.canVerify()) {
            logger.warn("OTP cannot be verified - expired or max attempts reached for identifier: {}", request.getIdentifier());
            return false;
        }

        otp.setAttempts(otp.getAttempts() + 1);

        if (!otp.getOtpCode().equals(request.getOtpCode())) {
            logger.warn("Invalid OTP code for identifier: {}", request.getIdentifier());
            
            if (otp.isMaxAttemptsReached()) {
                otp.setStatus(VerificationStatus.FAILED);
                logger.warn("Max OTP attempts reached for identifier: {}", request.getIdentifier());
            }
            
            otpRepository.save(otp);
            return false;
        }

        otp.setStatus(VerificationStatus.VERIFIED);
        otp.setVerifiedAt(LocalDateTime.now());
        otpRepository.save(otp);

        updateUserVerificationStatus(request.getIdentifier(), request.getOtpType());

        logger.info("OTP verified successfully for identifier: {}", request.getIdentifier());
        return true;
    }

    private void updateUserVerificationStatus(String identifier, String otpType) {
        Optional<User> userOpt = userRepository.findByIdentifier(identifier);
        if (userOpt.isEmpty()) {
            return;
        }

        User user = userOpt.get();
        boolean updated = false;

        if (isEmail(identifier) && user.getEmailVerificationStatus() != VerificationStatus.VERIFIED) {
            user.setEmailVerificationStatus(VerificationStatus.VERIFIED);
            user.setEmailVerifiedAt(LocalDateTime.now());
            updated = true;
        } else if (!isEmail(identifier) && user.getPhoneVerificationStatus() != VerificationStatus.VERIFIED) {
            user.setPhoneVerificationStatus(VerificationStatus.VERIFIED);
            user.setPhoneVerifiedAt(LocalDateTime.now());
            updated = true;
        }

        if (user.isEmailVerified() && user.isPhoneVerified()) {
            user.setIsVerified(true);
            updated = true;
        }

        if (updated) {
            userRepository.save(user);
            logger.info("Updated verification status for user: {}", user.getId());
        }
    }

    private String generateOtpCode() {
        Random random = new Random();
        StringBuilder otp = new StringBuilder();
        
        for (int i = 0; i < otpLength; i++) {
            otp.append(random.nextInt(10));
        }
        
        return otp.toString();
    }

    private boolean isEmail(String identifier) {
        return identifier.contains("@");
    }

    public void cleanupExpiredOtps() {
        logger.info("Cleaning up expired OTPs");
        int expiredCount = otpRepository.expireOldOtps(LocalDateTime.now());
        logger.info("Expired {} OTPs", expiredCount);

        // Del old OTPs (older than 7 days)
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(7);
        int deletedCount = otpRepository.deleteOldOtps(cutoffDate);
        logger.info("Deleted {} old OTPs", deletedCount);
    }

    public boolean hasValidOtp(String identifier, String otpType) {
        Optional<OtpVerification> otpOpt = otpRepository.findLatestPendingOtp(identifier, otpType);
        return otpOpt.isPresent() && otpOpt.get().canVerify();
    }
}
