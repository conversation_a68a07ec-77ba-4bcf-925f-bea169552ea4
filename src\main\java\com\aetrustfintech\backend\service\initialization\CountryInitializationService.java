package com.aetrustfintech.backend.service.initialization;

import com.aetrustfintech.backend.enums.Country;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.UserRepository;
import com.aetrustfintech.backend.service.notification.EmailService;
import com.aetrustfintech.backend.service.notification.SmsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class CountryInitializationService {

    private static final Logger logger = LoggerFactory.getLogger(CountryInitializationService.class);

    private final UserRepository userRepository;
    private final EmailService emailService;
    private final SmsService smsService;

    @Transactional
    public void initializeUserForCountry(User user) {
        logger.info("Initializing AUTH settings for user {} in country: {}", user.getId(), user.getCountryCode());

        try {
            Country country = Country.fromCode(user.getCountryCode());

            switch (country) {
                case ETHIOPIA:
                    initializeEthiopianAuthSettings(user);
                    break;
                case RWANDA:
                    initializeRwandanAuthSettings(user);
                    break;
                default:
                    logger.warn("No specific auth settings for country: {}", user.getCountryCode());
                    initializeDefaultAuthSettings(user);
            }

            sendWelcomeNotification(user, country);

            // Mark user as initialized
            user.setInitializedAt(LocalDateTime.now());
            userRepository.save(user);

            logger.info("Auth initialization completed for user: {}", user.getId());

        } catch (Exception e) {
            logger.error("Failed to initialize auth for user {} in country {}: {}",
                     user.getId(), user.getCountryCode(), e.getMessage(), e);
            throw new RuntimeException("Auth initialization failed", e);
        }
    }

    private void initializeEthiopianAuthSettings(User user) {
        logger.info("Setting up Ethiopian auth settings for user: {}", user.getId());

        // Set Ethiopia-specific auth settings
        // 2FA required for transactions > ETB 1,000
        // Data must stay in Ethiopia
        // Peer introduction required for Level 1

        // Set user metadata for Ethiopia compliance
        user.setCountrySpecificData("{\"twoFaThreshold\":1000,\"currency\":\"ETB\",\"dataLocalization\":true,\"peerIntroRequired\":true}");

        logger.info("Ethiopian auth settings completed for user: {}", user.getId());
    }

    private void initializeRwandanAuthSettings(User user) {
        logger.info("Setting up Rwandan auth settings for user: {}", user.getId());

        // Set Rwanda-specific auth settings
        // GDPR compliance required
        // NIDA verification required
        // 2FA required for transactions > RWF 100,000

        // Set user metadata for Rwanda compliance
        user.setCountrySpecificData("{\"twoFaThreshold\":100000,\"currency\":\"RWF\",\"gdprCompliance\":true,\"nidaRequired\":true}");

        logger.info("Rwandan auth settings completed for user: {}", user.getId());
    }

    private void initializeDefaultAuthSettings(User user) {
        logger.info("Setting up default auth settings for user: {}", user.getId());

        // Basic auth settings for other countries
        // Default 2FA threshold, no special compliance requirements

        user.setCountrySpecificData("{\"twoFaThreshold\":50000,\"currency\":\"USD\",\"defaultSettings\":true}");

        logger.info("Default auth settings completed for user: {}", user.getId());
    }

    private void initializeEthiopiaCompliance(UUID userId) {
        try {
            // Set up Ethiopia-specific compliance monitoring
            // - Data localization requirements
            // - Transaction limits based on account level
            // - 2FA requirements for transactions > ETB 1,000
            // - Float reconciliation requirements
            
            logger.debug("Ethiopia compliance rules initialized for user: {}", userId);
        } catch (Exception e) {
            logger.error("Failed to initialize Ethiopia compliance for user {}: {}", userId, e.getMessage());
        }
    }

    private void initializeRwandaCompliance(UUID userId) {
        try {
            // Set up Rwanda-specific compliance monitoring
            // - GDPR compliance requirements
            // - NIDA verification requirements
            // - AML/CFT screening
            // - Trust fund diversification rules
            
            logger.debug("Rwanda compliance rules initialized for user: {}", userId);
        } catch (Exception e) {
            logger.error("Failed to initialize Rwanda compliance for user {}: {}", userId, e.getMessage());
        }
    }

    private void sendWelcomeNotification(User user, Country country) {
        try {
            String welcomeMessage = buildWelcomeMessage(user, country);
            
            // Send welcome email
            notificationService.sendEmail(
                user.getEmail(),
                "Welcome to AeTrust",
                welcomeMessage
            );
            
            // Send welcome SMS
            notificationService.sendSms(
                user.getPhone(),
                "Welcome to AeTrust! Your account has been created successfully."
            );
            
            logger.info("Welcome notifications sent to user: {}", user.getId());

        } catch (Exception e) {
            logger.error("Failed to send welcome notification to user {}: {}", user.getId(), e.getMessage());
            // Don't throw exception here as it's not critical
        }
    }

    private String buildWelcomeMessage(User user, Country country) {
        StringBuilder message = new StringBuilder();
        message.append("Dear ").append(user.getFirstName()).append(",\n\n");
        message.append("Welcome to AeTrust! Your account has been successfully created.\n\n");
        
        switch (country) {
            case ETHIOPIA:
                message.append("As an Ethiopian user, you have access to:\n");
                message.append("- Mobile money transfers\n");
                message.append("- Bill payments\n");
                message.append("- Agent network services\n");
                message.append("- Compliance with NBE regulations\n\n");
                message.append("Your account starts at Level 1. Complete KYC to upgrade.\n");
                break;
                
            case RWANDA:
                message.append("As a Rwandan user, you have access to:\n");
                message.append("- NIDA-verified transactions\n");
                message.append("- Cross-border remittances\n");
                message.append("- GDPR-compliant data protection\n");
                message.append("- BNR-regulated services\n\n");
                message.append("Complete NIDA verification to unlock full features.\n");
                break;
                
            default:
                message.append("You now have access to our full range of financial services.\n");
        }
        
        message.append("\nNext steps:\n");
        message.append("1. Verify your email and phone number\n");
        message.append("2. Complete KYC verification\n");
        message.append("3. Set up your transaction PIN\n");
        message.append("4. Start using AeTrust services\n\n");
        message.append("Thank you for choosing AeTrust!\n\n");
        message.append("Best regards,\nThe AeTrust Team");
        
        return message.toString();
    }

    public void upgradeUserTier(UUID userId, String newTier) {
        logger.info("Upgrading user {} to tier: {}", userId, newTier);

        // This method can be called when user completes additional KYC steps
        // Implementation depends on specific business rules

        try {
            // Update user tier based on country
            // Send notification about tier upgrade
            // Update compliance rules if needed

            logger.info("User tier upgrade completed for user: {}", userId);
        } catch (Exception e) {
            logger.error("Failed to upgrade user tier for user {}: {}", userId, e.getMessage());
            throw new RuntimeException("Tier upgrade failed", e);
        }
    }
}
