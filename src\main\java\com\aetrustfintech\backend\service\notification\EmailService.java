package com.aetrustfintech.backend.service.notification;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    @Value("${spring.mail.username:}")
    private String fromEmail;

    public void sendOtpEmail(String toEmail, String otpCode, String otpType) {
        logger.info("Sending OTP email to: {} for type: {}", toEmail, otpType);
        
        // TODO: Implement actual email sending logic
        // For now, just log the OTP (remove in production)
        logger.info("OTP Code for {}: {}", toEmail, otpCode);
        
        String subject = getEmailSubject(otpType);
        String body = getEmailBody(otpCode, otpType);
        
        // Simulate email sending
        simulateEmailSending(toEmail, subject, body);
    }

    public void sendVerificationEmail(String toEmail, String firstName) {
        logger.info("Sending email verification to: {}", toEmail);

        String subject = "Verify Your Email - AeTrust Fintech";
        String body = String.format(
            "Dear %s,\n\n" +
            "Thank you for registering with AeTrust Fintech!\n\n" +
            "Please verify your email address by clicking the link below:\n" +
            "[Verification Link - To be implemented]\n\n" +
            "If you didn't create this account, please ignore this email.\n\n" +
            "This verification link will expire in 24 hours.\n\n" +
            "Best regards,\n" +
            "AeTrust Fintech Team",
            firstName
        );

        simulateEmailSending(toEmail, subject, body);
    }

    public void sendWelcomeEmail(String toEmail, String firstName) {
        logger.info("Sending welcome email to: {}", toEmail);
        
        String subject = "Welcome to AeTrust Fintech!";
        String body = String.format(
            "Dear %s,\n\n" +
            "Welcome to AeTrust Fintech! Your account has been successfully created.\n\n" +
            "You can now enjoy our comprehensive fintech services including:\n" +
            "- Digital wallet operations\n" +
            "- Secure payments and transfers\n" +
            "- Bill payments and airtime top-up\n" +
            "- Merchant services\n" +
            "- And much more!\n\n" +
            "Thank you for choosing AeTrust Fintech.\n\n" +
            "Best regards,\n" +
            "The AeTrust Team",
            firstName
        );
        
        simulateEmailSending(toEmail, subject, body);
    }

    public void sendPasswordResetEmail(String toEmail, String resetToken) {
        logger.info("Sending password reset email to: {}", toEmail);
        
        String subject = "Password Reset Request - AeTrust Fintech";
        String body = String.format(
            "Dear User,\n\n" +
            "You have requested to reset your password for your AeTrust Fintech account.\n\n" +
            "Your password reset code is: %s\n\n" +
            "This code will expire in 15 minutes for security reasons.\n\n" +
            "If you did not request this password reset, please ignore this email.\n\n" +
            "Best regards,\n" +
            "The AeTrust Team",
            resetToken
        );
        
        simulateEmailSending(toEmail, subject, body);
    }

    public void sendSecurityAlert(String toEmail, String alertMessage) {
        logger.info("Sending security alert email to: {}", toEmail);
        
        String subject = "Security Alert - AeTrust Fintech";
        String body = String.format(
            "Dear User,\n\n" +
            "We detected the following security event on your account:\n\n" +
            "%s\n\n" +
            "If this was you, no action is required. If you did not perform this action, " +
            "please contact our support team immediately.\n\n" +
            "Best regards,\n" +
            "The AeTrust Security Team",
            alertMessage
        );
        
        simulateEmailSending(toEmail, subject, body);
    }

    private String getEmailSubject(String otpType) {
        return switch (otpType) {
            case "REGISTRATION" -> "Verify Your Email - AeTrust Fintech";
            case "LOGIN" -> "Login Verification - AeTrust Fintech";
            case "RESET_PASSWORD" -> "Password Reset - AeTrust Fintech";
            case "TRANSACTION" -> "Transaction Verification - AeTrust Fintech";
            default -> "Verification Code - AeTrust Fintech";
        };
    }

    private String getEmailBody(String otpCode, String otpType) {
        String purpose = switch (otpType) {
            case "REGISTRATION" -> "complete your registration";
            case "LOGIN" -> "verify your login";
            case "RESET_PASSWORD" -> "reset your password";
            case "TRANSACTION" -> "authorize your transaction";
            default -> "verify your identity";
        };

        return String.format(
            "Dear User,\n\n" +
            "Your verification code to %s is: %s\n\n" +
            "This code will expire in 5 minutes for security reasons.\n\n" +
            "If you did not request this code, please ignore this email.\n\n" +
            "Best regards,\n" +
            "The AeTrust Team",
            purpose, otpCode
        );
    }

    private void simulateEmailSending(String toEmail, String subject, String body) {
        // TODO: email service integration 
        logger.info("Email sent successfully to: {} with subject: {}", toEmail, subject);
        logger.debug("Email body: {}", body);
    }
}
