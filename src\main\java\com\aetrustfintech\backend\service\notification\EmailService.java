package com.aetrustfintech.backend.service.notification;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.MailException;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import org.springframework.stereotype.Service;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    private final JavaMailSender mailSender;
    private final Executor emailExecutor;

    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;

    @Value("${spring.mail.enabled:true}")
    private boolean emailEnabled;

    @Value("${app.name:AeTrust Fintech}")
    private String appName;

    @Value("${app.support.email:<EMAIL>}")
    private String supportEmail;

    @Autowired
    public EmailService(JavaMailSender mailSender) {
        this.mailSender = mailSender;
        this.emailExecutor = Executors.newFixedThreadPool(5);
    }

    public void sendOtpEmail(String toEmail, String otpCode, String otpType) {
        logger.info("Sending OTP email to: {} for type: {}", toEmail, otpType);

        if (!emailEnabled) {
            logger.warn("Email service is disabled. OTP Code for {}: {}", toEmail, otpCode);
            return;
        }

        String subject = getEmailSubject(otpType);
        String body = getEmailBody(otpCode, otpType);

        sendEmailAsync(toEmail, subject, body);
    }

    public void sendOtpEmailSync(String toEmail, String otpCode, String otpType) {
        logger.info("Sending OTP email synchronously to: {} for type: {}", toEmail, otpType);

        if (!emailEnabled) {
            logger.warn("Email service is disabled. OTP Code for {}: {}", toEmail, otpCode);
            return;
        }

        String subject = getEmailSubject(otpType);
        String body = getEmailBody(otpCode, otpType);

        sendEmail(toEmail, subject, body);
    }

    public void sendVerificationEmail(String toEmail, String firstName, String verificationToken) {
        logger.info("Sending email verification to: {}", toEmail);

        String subject = String.format("Verify Your Email - %s", appName);
        String body = String.format(
            "Dear %s,\n\n" +
            "Thank you for registering with %s!\n\n" +
            "Please verify your email address by using the verification code below:\n\n" +
            "Verification Code: %s\n\n" +
            "If you didn't create this account, please ignore this email.\n\n" +
            "This verification code will expire in 24 hours.\n\n" +
            "If you need assistance, please contact our support team at %s\n\n" +
            "Best regards,\n" +
            "%s Team",
            firstName, appName, verificationToken, supportEmail, appName
        );

        sendEmailAsync(toEmail, subject, body);
    }

    public void sendWelcomeEmail(String toEmail, String firstName) {
        logger.info("Sending welcome email to: {}", toEmail);

        String subject = String.format("Welcome to %s!", appName);
        String body = String.format(
            "Dear %s,\n\n" +
            "Welcome to %s! Your account has been successfully created and verified.\n\n" +
            "You can now enjoy our comprehensive fintech services including:\n" +
            "• Digital wallet operations\n" +
            "• Secure payments and transfers\n" +
            "• Bill payments and airtime top-up\n" +
            "• Merchant services\n" +
            "• KYC verification and compliance\n" +
            "• Multi-currency support\n" +
            "• And much more!\n\n" +
            "To get started, please log in to your account and complete your profile setup.\n\n" +
            "If you have any questions, our support team is available 24/7 at %s\n\n" +
            "Thank you for choosing %s.\n\n" +
            "Best regards,\n" +
            "The %s Team",
            firstName, appName, supportEmail, appName, appName
        );

        sendEmailAsync(toEmail, subject, body);
    }

    public void sendPasswordResetEmail(String toEmail, String resetToken) {
        logger.info("Sending password reset email to: {}", toEmail);

        String subject = String.format("Password Reset Request - %s", appName);
        String body = String.format(
            "Dear User,\n\n" +
            "You have requested to reset your password for your %s account.\n\n" +
            "Your password reset code is: %s\n\n" +
            "This code will expire in 15 minutes for security reasons.\n\n" +
            "If you did not request this password reset, please:\n" +
            "• Ignore this email\n" +
            "• Consider changing your password if you suspect unauthorized access\n" +
            "• Contact our support team at %s if you have concerns\n\n" +
            "For your security, never share this reset code with anyone.\n\n" +
            "Best regards,\n" +
            "The %s Security Team",
            appName, resetToken, supportEmail, appName
        );

        sendEmailAsync(toEmail, subject, body);
    }

    public void sendSecurityAlert(String toEmail, String alertMessage) {
        logger.info("Sending security alert email to: {}", toEmail);

        String subject = String.format("🔒 Security Alert - %s", appName);
        String body = String.format(
            "Dear User,\n\n" +
            "We detected the following security event on your %s account:\n\n" +
            "🚨 ALERT: %s\n\n" +
            "If this was you, no action is required.\n\n" +
            "If you did NOT perform this action, please take immediate steps:\n" +
            "• Change your password immediately\n" +
            "• Review your recent account activity\n" +
            "• Enable two-factor authentication if not already active\n" +
            "• Contact our security team at %s\n\n" +
            "For your protection, we recommend regularly monitoring your account activity.\n\n" +
            "Best regards,\n" +
            "The %s Security Team",
            appName, alertMessage, supportEmail, appName
        );

        // Security alerts should be sent immediately and synchronously
        sendEmail(toEmail, subject, body);
    }

    private String getEmailSubject(String otpType) {
        return switch (otpType) {
            case "REGISTRATION" -> String.format("Verify Your Email - %s", appName);
            case "LOGIN" -> String.format("Login Verification - %s", appName);
            case "RESET_PASSWORD" -> String.format("Password Reset - %s", appName);
            case "TRANSACTION" -> String.format("Transaction Verification - %s", appName);
            default -> String.format("Verification Code - %s", appName);
        };
    }

    private String getEmailBody(String otpCode, String otpType) {
        String purpose = switch (otpType) {
            case "REGISTRATION" -> "complete your registration";
            case "LOGIN" -> "verify your login";
            case "RESET_PASSWORD" -> "reset your password";
            case "TRANSACTION" -> "authorize your transaction";
            default -> "verify your identity";
        };

        return String.format(
            "Dear User,\n\n" +
            "Your verification code to %s is: %s\n\n" +
            "This code will expire in 5 minutes for security reasons.\n\n" +
            "Important Security Notes:\n" +
            "• Never share this code with anyone\n" +
            "• %s will never ask for this code via phone or email\n" +
            "• If you did not request this code, please contact support at %s\n\n" +
            "Best regards,\n" +
            "The %s Team",
            purpose, otpCode, appName, supportEmail, appName
        );
    }

    
    private void sendEmailAsync(String toEmail, String subject, String body) {
        CompletableFuture.runAsync(() -> sendEmail(toEmail, subject, body), emailExecutor)
            .exceptionally(throwable -> {
                logger.error("Failed to send email asynchronously to: {}", toEmail, throwable);
                return null;
            });
    }

    
    public void sendEmail(String toEmail, String subject, String body) {
        try {
            if (!emailEnabled) {
                logger.warn("Email service is disabled. Would send email to: {} with subject: {}", toEmail, subject);
                return;
            }

            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(toEmail);
            message.setSubject(subject);
            message.setText(body);

            mailSender.send(message);
            logger.info("Email sent successfully to: {} with subject: {}", toEmail, subject);

        } catch (MailException e) {
            logger.error("Failed to send email to: {} with subject: {}", toEmail, subject, e);
            throw e; 
        }
    }


    private void sendHtmlEmail(String toEmail, String subject, String htmlBody) {
        try {
            if (!emailEnabled) {
                logger.warn("Email service is disabled. Would send HTML email to: {} with subject: {}", toEmail, subject);
                return;
            }

            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8");

            helper.setFrom(fromEmail);
            helper.setTo(toEmail);
            helper.setSubject(subject);
            helper.setText(htmlBody, true);

            mailSender.send(message);
            logger.info("HTML email sent successfully to: {} with subject: {}", toEmail, subject);

        } catch (MailException | MessagingException e) {
            logger.error("Failed to send HTML email to: {} with subject: {}", toEmail, subject, e);
            throw new RuntimeException("Failed to send HTML email", e);
        }
    }

   
    public boolean isValidEmail(String email) {
        return email != null &&
               email.matches("^[A-Za-z0-9+_.-]+@([A-Za-z0-9.-]+\\.[A-Za-z]{2,})$") &&
               email.length() <= 254;
    }

  
    public boolean isEmailServiceEnabled() {
        return emailEnabled;
    }
}
