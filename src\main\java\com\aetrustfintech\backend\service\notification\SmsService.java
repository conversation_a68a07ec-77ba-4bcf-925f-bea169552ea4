package com.aetrustfintech.backend.service.notification;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;

import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@Service
public class SmsService {

    private static final Logger logger = LoggerFactory.getLogger(SmsService.class);

    private final RestTemplate restTemplate;
    private final Executor smsExecutor;

    @Value("${sms.provider.api-key:}")
    private String smsApiKey;

    @Value("${sms.provider.sender-id:AeTrust}")
    private String senderId;

    @Value("${sms.provider.url:https://api.twilio.com/2010-04-01/Accounts}")
    private String smsProviderUrl;

    @Value("${sms.provider.account-sid:}")
    private String accountSid;

    @Value("${sms.enabled:true}")
    private boolean smsEnabled;

    @Value("${app.name:AeTrust Fintech}")
    private String appName;

    @Autowired
    public SmsService(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
        this.smsExecutor = Executors.newFixedThreadPool(3);
    }

    public void sendOtpSms(String phoneNumber, String otpCode, String otpType) {
        logger.info("Sending OTP SMS to: {} for type: {}", phoneNumber, otpType);

        if (!smsEnabled) {
            logger.warn("SMS service is disabled. OTP Code for {}: {}", phoneNumber, otpCode);
            return;
        }

        if (!isValidPhoneNumber(phoneNumber)) {
            logger.error("Invalid phone number format: {}", phoneNumber);
            throw new IllegalArgumentException("Invalid phone number format");
        }

        String message = getSmsMessage(otpCode, otpType);
        sendSmsAsync(phoneNumber, message);
    }

    public void sendOtpSmsSync(String phoneNumber, String otpCode, String otpType) {
        logger.info("Sending OTP SMS synchronously to: {} for type: {}", phoneNumber, otpType);

        if (!smsEnabled) {
            logger.warn("SMS service is disabled. OTP Code for {}: {}", phoneNumber, otpCode);
            return;
        }

        if (!isValidPhoneNumber(phoneNumber)) {
            logger.error("Invalid phone number format: {}", phoneNumber);
            throw new IllegalArgumentException("Invalid phone number format");
        }

        String message = getSmsMessage(otpCode, otpType);
        sendSms(phoneNumber, message);
    }

    public void sendWelcomeSms(String phoneNumber, String firstName) {
        logger.info("Sending welcome SMS to: {}", phoneNumber);

        if (!isValidPhoneNumber(phoneNumber)) {
            logger.error("Invalid phone number format: {}", phoneNumber);
            return;
        }

        String message = String.format(
            "Welcome to %s, %s! Your account is now active. " +
            "Enjoy secure digital banking services. Download our app for the best experience.",
            appName, firstName
        );

        sendSmsAsync(phoneNumber, message);
    }

    public void sendTransactionAlert(String phoneNumber, String transactionDetails) {
        logger.info("Sending transaction alert SMS to: {}", phoneNumber);

        if (!isValidPhoneNumber(phoneNumber)) {
            logger.error("Invalid phone number format: {}", phoneNumber);
            return;
        }

        String message = String.format(
            "%s Alert: %s. If this wasn't you, contact support immediately. " +
            "Thank you for using %s.",
            appName, transactionDetails, appName
        );

        // Transaction alerts should be sent immediately
        sendSms(phoneNumber, message);
    }

    public void sendSecurityAlert(String phoneNumber, String alertMessage) {
        logger.info("Sending security alert SMS to: {}", phoneNumber);

        if (!isValidPhoneNumber(phoneNumber)) {
            logger.error("Invalid phone number format: {}", phoneNumber);
            return;
        }

        String message = String.format(
            "🔒 %s Security Alert: %s. If this wasn't you, secure your account immediately. " +
            "Contact support if needed.",
            appName, alertMessage
        );

        // Security alerts should be sent immediately and synchronously
        sendSms(phoneNumber, message);
    }

    public void sendPasswordResetSms(String phoneNumber, String resetCode) {
        logger.info("Sending password reset SMS to: {}", phoneNumber);

        if (!isValidPhoneNumber(phoneNumber)) {
            logger.error("Invalid phone number format: {}", phoneNumber);
            return;
        }

        String message = String.format(
            "Your %s password reset code is: %s. " +
            "This code expires in 15 minutes. Do not share this code with anyone.",
            appName, resetCode
        );

        sendSmsAsync(phoneNumber, message);
    }

    private String getSmsMessage(String otpCode, String otpType) {
        String purpose = switch (otpType) {
            case "REGISTRATION" -> "registration";
            case "LOGIN" -> "login";
            case "RESET_PASSWORD" -> "password reset";
            case "TRANSACTION" -> "transaction";
            default -> "verification";
        };

        return String.format(
            "Your %s %s code is: %s. Valid for 5 minutes. Do not share this code.",
            appName, purpose, otpCode
        );
    }


    private void sendSmsAsync(String phoneNumber, String message) {
        CompletableFuture.runAsync(() -> sendSms(phoneNumber, message), smsExecutor)
            .exceptionally(throwable -> {
                logger.error("Failed to send SMS asynchronously to: {}", phoneNumber, throwable);
                return null;
            });
    }

  
    public void sendSms(String phoneNumber, String message) {
        try {
            if (!smsEnabled) {
                logger.warn("SMS service is disabled. Would send SMS to: {} with message: {}", phoneNumber, message);
                return;
            }

            if (smsApiKey == null || smsApiKey.isEmpty()) {
                logger.error("SMS API key is not configured");
                throw new IllegalStateException("SMS API key is not configured");
            }

            String formattedPhone = formatPhoneNumber(phoneNumber);

            String url = String.format("%s/%s/Messages.json", smsProviderUrl, accountSid);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.setBasicAuth(accountSid, smsApiKey);

            Map<String, String> requestBody = new HashMap<>();
            requestBody.put("From", senderId);
            requestBody.put("To", formattedPhone);
            requestBody.put("Body", message);

            StringBuilder formData = new StringBuilder();
            for (Map.Entry<String, String> entry : requestBody.entrySet()) {
                if (formData.length() > 0) {
                    formData.append("&");
                }
                formData.append(entry.getKey()).append("=").append(entry.getValue());
            }

            HttpEntity<String> request = new HttpEntity<>(formData.toString(), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("SMS sent successfully to: {}", phoneNumber);
            } else {
                logger.error("Failed to send SMS to: {}. Status: {}", phoneNumber, response.getStatusCode());
                throw new RestClientException("SMS sending failed with status: " + response.getStatusCode());
            }

        } catch (RestClientException e) {
            logger.error("Failed to send SMS to: {} with message: {}", phoneNumber, message, e);
            throw e; 
        } catch (Exception e) {
            logger.error("Unexpected error sending SMS to: {}", phoneNumber, e);
            throw new RuntimeException("Failed to send SMS", e);
        }
    }

  
    private void sendSmsWithFallback(String phoneNumber, String message) {
        logger.info("fallback SMS provider for: {}", phoneNumber);
    }

    public boolean isValidPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return false;
        }

        String cleaned = phoneNumber.replaceAll("[^+\\d]", "");

        return cleaned.matches("^\\+?[1-9]\\d{7,14}$") &&
               cleaned.length() >= 8 &&
               cleaned.length() <= 16;
    }

    public String formatPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            throw new IllegalArgumentException("Phone number cannot be null");
        }

        String cleaned = phoneNumber.replaceAll("[^+\\d]", "");

        if (!cleaned.startsWith("+") && !cleaned.startsWith("00")) {
            cleaned = "+" + cleaned;
        }

        if (cleaned.startsWith("00")) {
            cleaned = "+" + cleaned.substring(2);
        }

        return cleaned;
    }

  
    public boolean isSmsServiceEnabled() {
        return smsEnabled;
    }

 
    public boolean isSmsConfigured() {
        return smsApiKey != null && !smsApiKey.isEmpty() &&
               accountSid != null && !accountSid.isEmpty() &&
               senderId != null && !senderId.isEmpty();
    }


    public Map<String, Object> getSmsProviderInfo() {
        Map<String, Object> info = new HashMap<>();
        info.put("enabled", smsEnabled);
        info.put("configured", isSmsConfigured());
        info.put("senderId", senderId);
        info.put("providerUrl", smsProviderUrl);
        return info;
    }
}
