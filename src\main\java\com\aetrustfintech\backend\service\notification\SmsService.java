package com.aetrustfintech.backend.service.notification;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class SmsService {

    private static final Logger logger = LoggerFactory.getLogger(SmsService.class);

    @Value("${sms.provider.api-key:}")
    private String smsApiKey;

    @Value("${sms.provider.sender-id:AeTrust}")
    private String senderId;

    public void sendOtpSms(String phoneNumber, String otpCode, String otpType) {
        logger.info("Sending OTP SMS to: {} for type: {}", phoneNumber, otpType);
        
        // TODO: Implement  SMS sending logic
        logger.info("OTP Code for {}: {}", phoneNumber, otpCode);
        
        String message = getSmsMessage(otpCode, otpType);
        
        simulateSmsSending(phoneNumber, message);
    }

    public void sendWelcomeSms(String phoneNumber, String firstName) {
        logger.info("Sending welcome SMS to: {}", phoneNumber);
        
        String message = String.format(
            "Welcome to AeTrust Fintech, %s! Your account is now active. " +
            "Enjoy secure digital banking services. Download our app for the best experience.",
            firstName
        );
        
        simulateSmsSending(phoneNumber, message);
    }

    public void sendTransactionAlert(String phoneNumber, String transactionDetails) {
        logger.info("Sending transaction alert SMS to: {}", phoneNumber);
        
        String message = String.format(
            "AeTrust Alert: %s. If this wasn't you, contact support immediately. " +
            "Thank you for using AeTrust Fintech.",
            transactionDetails
        );
        
        simulateSmsSending(phoneNumber, message);
    }

    public void sendSecurityAlert(String phoneNumber, String alertMessage) {
        logger.info("Sending security alert SMS to: {}", phoneNumber);
        
        String message = String.format(
            "AeTrust Security Alert: %s. If this wasn't you, secure your account immediately. " +
            "Contact support if needed.",
            alertMessage
        );
        
        simulateSmsSending(phoneNumber, message);
    }

    public void sendPasswordResetSms(String phoneNumber, String resetCode) {
        logger.info("Sending password reset SMS to: {}", phoneNumber);
        
        String message = String.format(
            "Your AeTrust password reset code is: %s. " +
            "This code expires in 15 minutes. Do not share this code with anyone.",
            resetCode
        );
        
        simulateSmsSending(phoneNumber, message);
    }

    private String getSmsMessage(String otpCode, String otpType) {
        String purpose = switch (otpType) {
            case "REGISTRATION" -> "registration";
            case "LOGIN" -> "login";
            case "RESET_PASSWORD" -> "password reset";
            case "TRANSACTION" -> "transaction";
            default -> "verification";
        };

        return String.format(
            "Your AeTrust %s code is: %s. Valid for 5 minutes. Do not share this code.",
            purpose, otpCode
        );
    }

    private void simulateSmsSending(String phoneNumber, String message) {
        // TODO:  SMS service integration 
        logger.info("SMS sent successfully to: {}", phoneNumber);
        logger.debug("SMS message: {}", message);
        
        // Simulate API call delay
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    public boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber != null && 
               phoneNumber.matches("^\\+?[1-9]\\d{1,14}$") &&
               phoneNumber.length() >= 10 &&
               phoneNumber.length() <= 15;
    }

    public String formatPhoneNumber(String phoneNumber) {
        String cleaned = phoneNumber.replaceAll("[^+\\d]", "");
        
        if (!cleaned.startsWith("+") && !cleaned.startsWith("00")) {
            cleaned = "+" + cleaned;
        }
        
        return cleaned;
    }
}
