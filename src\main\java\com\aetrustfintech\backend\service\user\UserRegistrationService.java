package com.aetrustfintech.backend.service.user;

import com.aetrustfintech.backend.dto.auth.*;
import com.aetrustfintech.backend.enums.*;
import com.aetrustfintech.backend.exception.ValidationException;
import com.aetrustfintech.backend.exception.ResourceNotFoundException;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.model.UserRegistration;
import com.aetrustfintech.backend.repository.UserRegistrationRepository;
import com.aetrustfintech.backend.repository.UserRepository;
import com.aetrustfintech.backend.service.auth.HashingService;
import com.aetrustfintech.backend.service.initialization.CountryInitializationService;
import com.aetrustfintech.backend.service.notification.EmailService;
import com.aetrustfintech.backend.service.notification.SmsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
@Transactional
public class UserRegistrationService {

    private static final Logger logger = LoggerFactory.getLogger(UserRegistrationService.class);
    private static final int MAX_REGISTRATION_ATTEMPTS_PER_DAY = 3;

    private final UserRegistrationRepository registrationRepository;
    private final UserRepository userRepository;
    private final HashingService hashingService;
    private final com.aetrustfintech.backend.service.auth.OtpService otpService;
    private final EmailService emailService;
    private final SmsService smsService;
    private final CountryInitializationService countryInitService;

    @Autowired
    public UserRegistrationService(UserRegistrationRepository registrationRepository,
                                 UserRepository userRepository,
                                 HashingService hashingService,
                                 com.aetrustfintech.backend.service.auth.OtpService otpService,
                                 EmailService emailService,
                                 SmsService smsService,
                                 CountryInitializationService countryInitService) {
        this.registrationRepository = registrationRepository;
        this.userRepository = userRepository;
        this.hashingService = hashingService;
        this.otpService = otpService;
        this.emailService = emailService;
        this.smsService = smsService;
        this.countryInitService = countryInitService;
    }

    public RegistrationStatusResponse initiateRegistration(InitialRegistrationRequest request) {
        logger.info("Initiating registration for email: {}", hashingService.maskEmail(request.getEmail()));

        ExistingUserCheckResult existingUserCheck = checkExistingUser(request.getEmail(), request.getPhone());
        if (existingUserCheck.hasExistingUser()) {
            return existingUserCheck.getResponse();
        }

        // Check for incomplete reg
        Optional<UserRegistration> existingRegistration = registrationRepository.findByEmailOrPhone(
            request.getEmail(), request.getPhone());

        if (existingRegistration.isPresent() && !existingRegistration.get().getIsCompleted()) {
            UserRegistration registration = existingRegistration.get();
            logger.info("Found existing incomplete registration for user: {}", registration.getId());
            return convertToStatusResponseWithGuidance(registration);
        }

        checkRegistrationRateLimit(request.getEmail(), request.getPhone(), request.getRegistrationIpAddress());

        UserRegistration registration = new UserRegistration(
            request.getPlatform(),
            request.getFirstName(),
            request.getLastName(),
            request.getEmail(),
            request.getPhone(),
            request.getDateOfBirth(),
            hashingService.hashPassword(request.getPassword()),
            request.getCountryCode()
        );

        registration.setIntendedRole(request.getIntendedRole());
        registration.setReferralCode(request.getReferralCode());
        registration.setRegistrationIpAddress(request.getRegistrationIpAddress());
        registration.setDeviceInfo(request.getDeviceInfo());
        registration.setUserAgent(request.getUserAgent());

        registration = registrationRepository.save(registration);

        otpService.sendOtp(request.getPhone(), "REGISTRATION");

        logger.info("Registration initiated successfully with ID: {}", registration.getId());
        return convertToStatusResponse(registration);
    }

    public RegistrationStatusResponse verifyPhoneNumber(Long registrationId, String otpCode) {
        logger.info("Verifying phone number for registration ID: {}", registrationId);

        UserRegistration registration = getRegistrationById(registrationId);
        
        if (registration.getCurrentStep() != RegistrationStep.PHONE_VERIFICATION) {
            throw new ValidationException("Phone verification is not the current step");
        }

        OtpRequest otpRequest = new OtpRequest();
        otpRequest.setIdentifier(registration.getPhone());
        otpRequest.setOtpCode(otpCode);
        otpRequest.setOtpType("REGISTRATION");

        if (!otpService.verifyOtp(otpRequest)) {
            throw new ValidationException("Invalid or expired OTP code");
        }

        registration.setPhoneVerificationStatus(VerificationStatus.VERIFIED);
        registration.setPhoneVerifiedAt(LocalDateTime.now());
        registration.setCurrentStep(RegistrationStep.EMAIL_VERIFICATION);

        registration = registrationRepository.save(registration);

        otpService.sendOtp(registration.getEmail(), "REGISTRATION");

        logger.info("Phone verification completed for registration ID: {}", registrationId);
        return convertToStatusResponse(registration);
    }

    public RegistrationStatusResponse verifyEmail(Long registrationId, String otpCode) {
        logger.info("Verifying email for registration ID: {}", registrationId);

        UserRegistration registration = getRegistrationById(registrationId);
        
        if (registration.getCurrentStep() != RegistrationStep.EMAIL_VERIFICATION) {
            throw new ValidationException("Email verification is not the current step");
        }

        OtpRequest otpRequest = new OtpRequest();
        otpRequest.setIdentifier(registration.getEmail());
        otpRequest.setOtpCode(otpCode);
        otpRequest.setOtpType("REGISTRATION");

        if (!otpService.verifyOtp(otpRequest)) {
            throw new ValidationException("Invalid or expired OTP code");
        }

        registration.setEmailVerificationStatus(VerificationStatus.VERIFIED);
        registration.setEmailVerifiedAt(LocalDateTime.now());
        registration.setCurrentStep(RegistrationStep.IDENTITY_VERIFICATION);

        registration = registrationRepository.save(registration);

        logger.info("Email verification completed for registration ID: {}", registrationId);
        return convertToStatusResponse(registration);
    }

    public RegistrationStatusResponse submitIdentityVerification(IdentityVerificationRequest request) {
        logger.info("Submitting identity verification for registration ID: {}", request.getRegistrationId());

        UserRegistration registration = getRegistrationById(request.getRegistrationId());
        
        if (registration.getCurrentStep() != RegistrationStep.IDENTITY_VERIFICATION) {
            throw new ValidationException("Identity verification is not the current step");
        }

        validateIdFormat(request, registration.getCountryCode());
        registration.setIdType(request.getIdType().getValue());
        registration.setIdNumberHash(hashingService.hashPiiData(request.getIdNumber()));
        
        String imagePath = storeIdImage(request.getIdImageBase64(), registration.getId());
        registration.setIdImagePath(imagePath);

        registration.setIdVerificationStatus(VerificationStatus.UNDER_REVIEW);
        registration.setCurrentStep(RegistrationStep.TRANSACTION_PIN);

        registration = registrationRepository.save(registration);

        logger.info("Identity verification submitted for registration ID: {}", request.getRegistrationId());
        return convertToStatusResponse(registration);
    }

    public RegistrationStatusResponse setTransactionPin(TransactionPinRequest request) {
        logger.info("Setting transaction PIN for registration ID: {}", request.getRegistrationId());

        UserRegistration registration = getRegistrationById(request.getRegistrationId());
        
        if (registration.getCurrentStep() != RegistrationStep.TRANSACTION_PIN) {
            throw new ValidationException("Transaction PIN setup is not the current step");
        }

        if (!request.isPinMatching()) {
            throw new ValidationException("PIN and confirmation PIN do not match");
        }

        if (!request.isSecurePin()) {
            throw new ValidationException("PIN is not secure. Please choose a different PIN");
        }

        registration.setTransactionPinHash(hashingService.hashTransactionPin(request.getPin()));
        registration.setPinSet(true);

        if (registration.getIntendedRole() == UserRole.AGENT) {
            registration.setCurrentStep(RegistrationStep.BUSINESS_VERIFICATION);
        } else {
            registration.setCurrentStep(RegistrationStep.COMPLETED);
        }

        registration = registrationRepository.save(registration);

        logger.info("Transaction PIN set for registration ID: {}", request.getRegistrationId());
        return convertToStatusResponse(registration);
    }

    public RegistrationStatusResponse setBiometricStatus(BiometricStatusRequest request) {
        logger.info("Setting biometric status for registration ID: {}", request.getRegistrationId());

        UserRegistration registration = getRegistrationById(request.getRegistrationId());

        registration.setBiometricEnabled(request.getBiometricEnabled());
        
        if (request.getBiometricEnabled() && request.getBiometricTemplate() != null) {
            registration.setBiometricData(hashingService.encryptSensitiveData(request.getBiometricTemplate()));
        }

        registration = registrationRepository.save(registration);

        logger.info("Biometric status updated for registration ID: {}", request.getRegistrationId());
        return convertToStatusResponse(registration);
    }

    public RegistrationStatusResponse completeRegistration(Long registrationId) {
        logger.info("Completing registration for ID: {}", registrationId);

        UserRegistration registration = getRegistrationById(registrationId);

        if (!registration.isReadyForCompletion()) {
            throw new ValidationException("Registration is not ready for completion. Please complete all required steps.");
        }

        User user = createUserFromRegistration(registration);
        user = userRepository.save(user);

        // Initialize country-specific auth settings
        countryInitService.initializeUserForCountry(user);

        registration.setIsCompleted(true);
        registration.setCompletedUserId(user.getId());
        registration.setCurrentStep(RegistrationStep.COMPLETED);
        registrationRepository.save(registration);

        logger.info("Registration completed successfully. User ID: {}, Registration ID: {}", 
                   user.getId(), registrationId);

        return convertToStatusResponse(registration);
    }

    public RegistrationStatusResponse getRegistrationStatus(Long registrationId) {
        UserRegistration registration = getRegistrationById(registrationId);
        return convertToStatusResponse(registration);
    }

    private UserRegistration getRegistrationById(Long registrationId) {
        return registrationRepository.findById(registrationId)
            .orElseThrow(() -> new ResourceNotFoundException("Registration not found with ID: " + registrationId));
    }

    private void checkRegistrationRateLimit(String email, String phone, String ipAddress) {
        LocalDateTime oneDayAgo = LocalDateTime.now().minusDays(1);
        
        Long emailAttempts = registrationRepository.countRecentRegistrationAttemptsByEmail(email, oneDayAgo);
        Long phoneAttempts = registrationRepository.countRecentRegistrationAttemptsByPhone(phone, oneDayAgo);
        
        if (emailAttempts >= MAX_REGISTRATION_ATTEMPTS_PER_DAY) {
            throw new ValidationException("Too many registration attempts with this email. Please try again tomorrow.");
        }
        
        if (phoneAttempts >= MAX_REGISTRATION_ATTEMPTS_PER_DAY) {
            throw new ValidationException("Too many registration attempts with this phone number. Please try again tomorrow.");
        }
    }

    private void validateIdFormat(IdentityVerificationRequest request, String countryCode) {
        // ID format validation disabled - allow any number format
        // if ("ET".equals(countryCode) && !request.isValidEthiopianId()) {
        //     throw new ValidationException("Invalid Ethiopian ID format");
        // }
        // if ("RW".equals(countryCode) && !request.isValidRwandanId()) {
        //     throw new ValidationException("Invalid Rwandan ID format");
        // }
        // if (request.getIdType() == IdType.PASSPORT && !request.isValidPassport()) {
        //     throw new ValidationException("Invalid passport format");
        // }
    }

    private String storeIdImage(String imageBase64, Long registrationId) {
    
        return "/secure/id-images/" + registrationId + "_" + System.currentTimeMillis() + ".jpg";
    }

    private User createUserFromRegistration(UserRegistration registration) {
        User user = new User();
        user.setRegistrationId(registration.getId()); 
        user.setEmail(registration.getEmail());
        user.setPhone(registration.getPhone());
        user.setFirstName(registration.getFirstName());
        user.setLastName(registration.getLastName());
        user.setDateOfBirth(registration.getDateOfBirth());
        user.setPassword(registration.getPasswordHash());
        user.setRole(registration.getIntendedRole());
        user.setCountryCode(registration.getCountryCode());
        
        if ("ET".equals(registration.getCountryCode())) {
            user.setKycLevel(KycLevel.LEVEL_1); // Ethiopian NBE Level 1
        } else {
            user.setKycLevel(KycLevel.LEVEL_1); // Default to Level 1
        }
        
        user.setAccountStatus(AccountStatus.ACTIVE);
        user.setEmailVerificationStatus(VerificationStatus.VERIFIED);
        user.setPhoneVerificationStatus(VerificationStatus.VERIFIED);
        user.setIsVerified(true);
        user.setEmailVerifiedAt(registration.getEmailVerifiedAt());
        user.setPhoneVerifiedAt(registration.getPhoneVerifiedAt());
        user.setTransactionPin(registration.getTransactionPinHash());
        user.setPinSet(registration.getPinSet());
        user.setBiometricEnabled(registration.getBiometricEnabled());
        user.setWalletBalance(BigDecimal.ZERO);
        
        if ("ET".equals(registration.getCountryCode())) {
            user.setCurrencyCode("ETB");
        } else if ("RW".equals(registration.getCountryCode())) {
            user.setCurrencyCode("RWF");
        } else {
            user.setCurrencyCode("USD");
        }
        
        return user;
    }

    private RegistrationStatusResponse convertToStatusResponse(UserRegistration registration) {
        RegistrationStatusResponse response = new RegistrationStatusResponse();
        response.setRegistrationId(registration.getId());
        response.setFirstName(registration.getFirstName());
        response.setLastName(registration.getLastName());
        response.setEmail(registration.getEmail());
        response.setPhone(registration.getPhone());
        response.setCountryCode(registration.getCountryCode());
        response.setCurrentStep(registration.getCurrentStep());
        response.setNextStep(registration.getNextStep());
        response.setCanProceed(registration.canProceedToNextStep());
        response.setIsCompleted(registration.getIsCompleted());
        response.setPhoneVerificationStatus(registration.getPhoneVerificationStatus());
        response.setEmailVerificationStatus(registration.getEmailVerificationStatus());
        response.setIdVerificationStatus(registration.getIdVerificationStatus());
        response.setPinSet(registration.getPinSet());
        response.setBiometricEnabled(registration.getBiometricEnabled());
        response.setPhoneVerifiedAt(registration.getPhoneVerifiedAt());
        response.setEmailVerifiedAt(registration.getEmailVerifiedAt());
        response.setIdVerifiedAt(registration.getIdVerifiedAt());
        response.setCreatedAt(registration.getCreatedAt());
        response.setUpdatedAt(registration.getUpdatedAt());

        return response;
    }

    private ExistingUserCheckResult checkExistingUser(String email, String phone) {
        Optional<User> existingUserByEmail = userRepository.findByEmail(email);
        Optional<User> existingUserByPhone = userRepository.findByPhone(phone);

        if (existingUserByEmail.isPresent()) {
            User user = existingUserByEmail.get();
            return ExistingUserCheckResult.withExistingUser(createExistingUserResponse(user, "email"));
        }

        if (existingUserByPhone.isPresent()) {
            User user = existingUserByPhone.get();
            return ExistingUserCheckResult.withExistingUser(createExistingUserResponse(user, "phone"));
        }

        return ExistingUserCheckResult.noExistingUser();
    }

    private RegistrationStatusResponse createExistingUserResponse(User user, String conflictField) {
        RegistrationStatusResponse response = new RegistrationStatusResponse();
        response.setRegistrationId(user.getRegistrationId());
        response.setCurrentStep(RegistrationStep.COMPLETED);
        response.setIsCompleted(true);
        response.setCanProceed(false);

        String message = String.format("Account already exists with this %s. ", conflictField);
        String nextAction = "";

        if (user.getIsVerified()) {
            nextAction = "You can log in directly using your credentials.";
        } else {
            nextAction = "Please complete your account verification or contact support.";
        }

        response.setMessage(message + nextAction);
        response.setNextAction(user.getIsVerified() ? "LOGIN" : "VERIFY_ACCOUNT");

        return response;
    }

    private RegistrationStatusResponse convertToStatusResponseWithGuidance(UserRegistration registration) {
        RegistrationStatusResponse response = convertToStatusResponse(registration);

        String guidance = getStepGuidance(registration.getCurrentStep(), registration);
        response.setMessage(guidance);
        response.setNextAction(getNextAction(registration.getCurrentStep()));

        return response;
    }

    private String getStepGuidance(RegistrationStep currentStep, UserRegistration registration) {
        switch (currentStep) {
            case PHONE_VERIFICATION:
                return "Please verify your phone number. Check your SMS for the verification code. " +
                       "If you didn't receive it, you can request a new code.";

            case EMAIL_VERIFICATION:
                return "Please verify your email address. Check your inbox for the verification link. " +
                       "Don't forget to check your spam folder.";

            case IDENTITY_VERIFICATION:
                String idStatus = registration.getIdVerificationStatus() != null ?
                    registration.getIdVerificationStatus().toString() : "PENDING";

                switch (idStatus) {
                    case "PENDING":
                        return "Please upload your ID document for verification. Ensure the image is clear and all details are visible.";
                    case "UNDER_REVIEW":
                        return "Your ID is currently under review. This usually takes 1-2 business days. You'll be notified once it's processed.";
                    case "REJECTED":
                        return "Your ID verification was rejected. Please upload a new, clear image of your ID document.";
                    default:
                        return "Please complete your ID verification.";
                }

            case TRANSACTION_PIN:
                return "Please set your transaction PIN. This will be used to authorize transactions and sensitive operations.";

            case BUSINESS_VERIFICATION:
                return "Please complete your business verification by providing required business documents.";

            case COMPLETED:
                return "Your registration is complete! You can now log in to your account.";

            default:
                return "Please continue with your registration process.";
        }
    }

    private String getNextAction(RegistrationStep currentStep) {
        switch (currentStep) {
            case PHONE_VERIFICATION:
                return "VERIFY_PHONE";
            case EMAIL_VERIFICATION:
                return "VERIFY_EMAIL";
            case IDENTITY_VERIFICATION:
                return "UPLOAD_ID";
            case TRANSACTION_PIN:
                return "SET_PIN";
            case BUSINESS_VERIFICATION:
                return "COMPLETE_BUSINESS_VERIFICATION";
            case COMPLETED:
                return "LOGIN";
            default:
                return "CONTINUE_REGISTRATION";
        }
    }

    public RegistrationStatusResponse resendVerification(String email, String phone, String verificationType) {
        logger.info("Resending verification for email: {}, type: {}", hashingService.maskEmail(email), verificationType);

        Optional<UserRegistration> registrationOpt = registrationRepository.findByEmailOrPhone(email, phone);
        if (registrationOpt.isEmpty()) {
            throw new ResourceNotFoundException("No registration found for the provided email or phone");
        }

        UserRegistration registration = registrationOpt.get();

        if (registration.getIsCompleted()) {
            throw new ValidationException("Registration is already completed. Please log in instead.");
        }

        switch (verificationType.toUpperCase()) {
            case "PHONE":
                if (registration.getPhoneVerificationStatus() == VerificationStatus.VERIFIED) {
                    throw new ValidationException("Phone number is already verified");
                }
                otpService.sendOtp(registration.getPhone(), "REGISTRATION");
                break;

            case "EMAIL":
                if (registration.getEmailVerificationStatus() == VerificationStatus.VERIFIED) {
                    throw new ValidationException("Email address is already verified");
                }
                emailService.sendVerificationEmail(registration.getEmail(), registration.getFirstName(), "verification-token-" + registration.getId());
                break;

            default:
                throw new ValidationException("Invalid verification type. Use 'PHONE' or 'EMAIL'");
        }

        logger.info("Verification resent successfully for registration: {}", registration.getId());
        return convertToStatusResponseWithGuidance(registration);
    }

    public RegistrationStatusResponse getRegistrationStatus(String email, String phone) {
        logger.info("Getting registration status for email: {}", hashingService.maskEmail(email));

        ExistingUserCheckResult existingUserCheck = checkExistingUser(email, phone);
        if (existingUserCheck.hasExistingUser()) {
            return existingUserCheck.getResponse();
        }

        Optional<UserRegistration> registrationOpt = registrationRepository.findByEmailOrPhone(email, phone);
        if (registrationOpt.isEmpty()) {
            throw new ResourceNotFoundException("No registration found for the provided email or phone");
        }

        UserRegistration registration = registrationOpt.get();
        return convertToStatusResponseWithGuidance(registration);
    }
}
