package com.aetrustfintech.backend.service.user;

import com.aetrustfintech.backend.dto.auth.UpdatePasswordRequest;
import com.aetrustfintech.backend.dto.user.UserResponse;
import com.aetrustfintech.backend.enums.AccountStatus;
import com.aetrustfintech.backend.exception.ResourceNotFoundException;
import com.aetrustfintech.backend.exception.ValidationException;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    @Autowired
    public UserService(UserRepository userRepository, PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }

    public UserResponse getUserById(UUID id) {
        logger.info("Fetching user by ID: {}", id);

        User user = userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));

        return convertToResponse(user);
    }

    public User getUserEntityById(UUID id) {
        logger.info("Fetching user entity by ID: {}", id);

        return userRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + id));
    }

    public UserResponse getUserByEmail(String email) {
        logger.info("Fetching user by email: {}", email);
        
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));
        
        return convertToResponse(user);
    }

    public UserResponse getUserByPhone(String phone) {
        logger.info("Fetching user by phone: {}", phone);
        
        User user = userRepository.findByPhone(phone)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with phone: " + phone));
        
        return convertToResponse(user);
    }

    public UserResponse getUserByIdentifier(String identifier) {
        logger.info("Fetching user by identifier: {}", identifier);
        
        User user = userRepository.findByIdentifier(identifier)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with identifier: " + identifier));
        
        return convertToResponse(user);
    }

    public Page<UserResponse> getAllUsers(Pageable pageable) {
        logger.info("Fetching all users with pagination");
        
        Page<User> users = userRepository.findAll(pageable);
        return users.map(this::convertToResponse);
    }

    public Page<UserResponse> getActiveVerifiedUsers(Pageable pageable) {
        logger.info("Fetching active verified users");
        
        Page<User> users = userRepository.findActiveVerifiedUsers(pageable);
        return users.map(this::convertToResponse);
    }

    public Page<UserResponse> searchUsers(String searchTerm, Pageable pageable) {
        logger.info("Searching users with term: {}", searchTerm);
        
        Page<User> users = userRepository.searchUsers(searchTerm, pageable);
        return users.map(this::convertToResponse);
    }

    public UserResponse updateProfile(UUID userId, UserResponse updateRequest) {
        logger.info("Updating profile for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        if (updateRequest.getFirstName() != null) {
            user.setFirstName(updateRequest.getFirstName());
        }
        if (updateRequest.getLastName() != null) {
            user.setLastName(updateRequest.getLastName());
        }
        if (updateRequest.getBio() != null) {
            user.setBio(updateRequest.getBio());
        }
        if (updateRequest.getAddress() != null) {
            user.setAddress(updateRequest.getAddress());
        }
        if (updateRequest.getLanguagePreference() != null) {
            user.setLanguagePreference(updateRequest.getLanguagePreference());
        }

        user = userRepository.save(user);
        logger.info("Profile updated successfully for user ID: {}", userId);
        
        return convertToResponse(user);
    }

    public void updatePassword(UUID userId, UpdatePasswordRequest request) {
        logger.info("Updating password for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        if (!passwordEncoder.matches(request.getCurrentPassword(), user.getPassword())) {
            throw new ValidationException("Current password is incorrect");
        }

        if (passwordEncoder.matches(request.getNewPassword(), user.getPassword())) {
            throw new ValidationException("New password must be different from current password");
        }

        // Update password
        user.setPassword(passwordEncoder.encode(request.getNewPassword()));
        user.setPasswordChangedAt(LocalDateTime.now());
        userRepository.save(user);

        logger.info("Password updated successfully for user ID: {}", userId);
    }

    public void updateProfilePicture(UUID userId, String profilePictureUrl) {
        logger.info("Updating profile picture for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        user.setProfilePicture(profilePictureUrl);
        userRepository.save(user);

        logger.info("Profile picture updated successfully for user ID: {}", userId);
    }

    public void enableTwoFactor(UUID userId) {
        logger.info("Enabling two-factor authentication for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        user.setTwoFactorEnabled(true);
        userRepository.save(user);

        logger.info("Two-factor authentication enabled for user ID: {}", userId);
    }

    public void disableTwoFactor(UUID userId) {
        logger.info("Disabling two-factor authentication for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        user.setTwoFactorEnabled(false);
        userRepository.save(user);

        logger.info("Two-factor authentication disabled for user ID: {}", userId);
    }

    public void setTransactionPin(UUID userId, String pin) {
        logger.info("Setting transaction PIN for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        user.setTransactionPin(passwordEncoder.encode(pin));
        user.setPinSet(true);
        userRepository.save(user);

        logger.info("Transaction PIN set successfully for user ID: {}", userId);
    }

    public boolean verifyTransactionPin(UUID userId, String pin) {
        logger.info("Verifying transaction PIN for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        if (!user.getPinSet() || user.getTransactionPin() == null) {
            return false;
        }

        return passwordEncoder.matches(pin, user.getTransactionPin());
    }

    public void suspendUser(UUID userId, String reason) {
        logger.info("Suspending user ID: {} for reason: {}", userId, reason);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        user.setAccountStatus(AccountStatus.SUSPENDED);
        userRepository.save(user);

        logger.info("User ID: {} suspended successfully", userId);
    }

    public void activateUser(UUID userId) {
        logger.info("Activating user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        user.setAccountStatus(AccountStatus.ACTIVE);
        user.setLoginAttempts(0);
        user.setAccountLockedUntil(null);
        userRepository.save(user);

        logger.info("User ID: {} activated successfully", userId);
    }

    public void deleteAccount(UUID userId) {
        logger.info("Deleting account for user ID: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with ID: " + userId));

        // Anonymize user data for GDPR compliance
        user.setEmail("deleted_" + userId + "@deleted.local");
        user.setPhone("DELETED");
        user.setFirstName("DELETED");
        user.setLastName("DELETED");
        user.setAccountStatus(AccountStatus.CLOSED);
        userRepository.save(user);
        
        logger.info("Account deleted successfully for user ID: {}", userId);
    }

    public UserResponse convertToResponse(User user) {
        UserResponse response = new UserResponse();
        response.setId(user.getId());
        response.setRegistrationId(user.getRegistrationId());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setUsername(user.getUsername());
        response.setFirstName(user.getFirstName());
        response.setLastName(user.getLastName());
        response.setFullName(user.getFullName());
        response.setProfilePicture(user.getProfilePicture());
        response.setBio(user.getBio());
        response.setAddress(user.getAddress());
        response.setRole(user.getRole());
        response.setAccountStatus(user.getAccountStatus());
        response.setKycStatus(user.getKycStatus());
        response.setEmailVerificationStatus(user.getEmailVerificationStatus());
        response.setPhoneVerificationStatus(user.getPhoneVerificationStatus());
        response.setIsVerified(user.getIsVerified());
        response.setKycStatus(user.getKycStatus());
        response.setWalletBalance(user.getWalletBalance());
        response.setCountryCode(user.getCountryCode());
        response.setCurrencyCode(user.getCurrencyCode());
        response.setLanguagePreference(user.getLanguagePreference());
        response.setTwoFactorEnabled(user.getTwoFactorEnabled());
        response.setBiometricEnabled(user.getBiometricEnabled());
        response.setPinSet(user.getPinSet());
        response.setLastLogin(user.getLastLogin());
        response.setEmailVerifiedAt(user.getEmailVerifiedAt());
        response.setPhoneVerifiedAt(user.getPhoneVerifiedAt());
        response.setKycCompletedAt(user.getKycCompletedAt());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());
        
        return response;
    }
}
