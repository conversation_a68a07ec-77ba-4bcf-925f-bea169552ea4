package com.aetrustfintech.backend.gateway.filter;

import io.github.bucket4j.Bucket;
import io.github.bucket4j.redis.lettuce.cas.LettuceBasedProxyManager;
import jakarta.servlet.FilterChain;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RateLimitFilterTest {

    @Mock
    private LettuceBasedProxyManager<String> proxyManager;

    @Mock
    private Bucket bucket;

    @Mock
    private HttpServletRequest request;

    @Mock
    private HttpServletResponse response;

    @Mock
    private FilterChain filterChain;

    @InjectMocks
    private RateLimitFilter rateLimitFilter;

    @BeforeEach
    void setUp() {
        when(proxyManager.builder()).thenReturn(mock(LettuceBasedProxyManager.ProxyManagerBuilder.class));
    }

    @Test
    void doFilterInternal_AnonymousUser_WithinLimit() throws Exception {
        // Arrange
        when(request.getRemoteAddr()).thenReturn("192.168.1.1");
        when(request.getAttribute("userId")).thenReturn(null);
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(true);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(filterChain).doFilter(request, response);
        verify(response, never()).setStatus(HttpServletResponse.SC_TOO_MANY_REQUESTS);
    }

    @Test
    void doFilterInternal_AnonymousUser_ExceedsLimit() throws Exception {
        // Arrange
        when(request.getRemoteAddr()).thenReturn("192.168.1.1");
        when(request.getAttribute("userId")).thenReturn(null);
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(false);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(response).setStatus(HttpServletResponse.SC_TOO_MANY_REQUESTS);
        verify(response).setContentType("application/json");
        verify(filterChain, never()).doFilter(request, response);
    }

    @Test
    void doFilterInternal_AuthenticatedUser_WithinLimit() throws Exception {
        // Arrange
        when(request.getAttribute("userId")).thenReturn("user123");
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(true);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(filterChain).doFilter(request, response);
        verify(response, never()).setStatus(HttpServletResponse.SC_TOO_MANY_REQUESTS);
    }

    @Test
    void doFilterInternal_AuthenticatedUser_ExceedsLimit() throws Exception {
        // Arrange
        when(request.getAttribute("userId")).thenReturn("user123");
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(false);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(response).setStatus(HttpServletResponse.SC_TOO_MANY_REQUESTS);
        verify(response).setContentType("application/json");
        verify(filterChain, never()).doFilter(request, response);
    }

    @Test
    void doFilterInternal_BucketCreationError() throws Exception {
        // Arrange
        when(request.getRemoteAddr()).thenReturn("192.168.1.1");
        when(request.getAttribute("userId")).thenReturn(null);
        when(proxyManager.getProxy(anyString(), any())).thenThrow(new RuntimeException("Redis connection failed"));

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert - Should allow request to proceed if rate limiting fails
        verify(filterChain).doFilter(request, response);
        verify(response, never()).setStatus(HttpServletResponse.SC_TOO_MANY_REQUESTS);
    }

    @Test
    void doFilterInternal_NullRemoteAddr() throws Exception {
        // Arrange
        when(request.getRemoteAddr()).thenReturn(null);
        when(request.getAttribute("userId")).thenReturn(null);
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(true);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(proxyManager).getProxy(eq("anonymous:unknown"), any());
        verify(filterChain).doFilter(request, response);
    }

    @Test
    void doFilterInternal_EmptyRemoteAddr() throws Exception {
        // Arrange
        when(request.getRemoteAddr()).thenReturn("");
        when(request.getAttribute("userId")).thenReturn(null);
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(true);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(proxyManager).getProxy(eq("anonymous:unknown"), any());
        verify(filterChain).doFilter(request, response);
    }

    @Test
    void doFilterInternal_UserIdAsString() throws Exception {
        // Arrange
        when(request.getAttribute("userId")).thenReturn("user-uuid-123");
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(true);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(proxyManager).getProxy(eq("user:user-uuid-123"), any());
        verify(filterChain).doFilter(request, response);
    }

    @Test
    void doFilterInternal_MultipleRequests_DifferentUsers() throws Exception {
        // Test that different users get different buckets
        
        // First user
        when(request.getAttribute("userId")).thenReturn("user1");
        when(proxyManager.getProxy(eq("user:user1"), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(true);

        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Second user
        when(request.getAttribute("userId")).thenReturn("user2");
        when(proxyManager.getProxy(eq("user:user2"), any())).thenReturn(bucket);

        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(proxyManager).getProxy(eq("user:user1"), any());
        verify(proxyManager).getProxy(eq("user:user2"), any());
        verify(filterChain, times(2)).doFilter(request, response);
    }

    @Test
    void doFilterInternal_RateLimitHeaders_Success() throws Exception {
        // Arrange
        when(request.getAttribute("userId")).thenReturn("user123");
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(true);
        when(bucket.getAvailableTokens()).thenReturn(199L);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(response).setHeader("X-RateLimit-Remaining", "199");
        verify(response).setHeader(eq("X-RateLimit-Reset"), anyString());
        verify(filterChain).doFilter(request, response);
    }

    @Test
    void doFilterInternal_RateLimitHeaders_Exceeded() throws Exception {
        // Arrange
        when(request.getAttribute("userId")).thenReturn("user123");
        when(proxyManager.getProxy(anyString(), any())).thenReturn(bucket);
        when(bucket.tryConsume(1)).thenReturn(false);
        when(bucket.getAvailableTokens()).thenReturn(0L);

        // Act
        rateLimitFilter.doFilterInternal(request, response, filterChain);

        // Assert
        verify(response).setHeader("X-RateLimit-Remaining", "0");
        verify(response).setHeader(eq("X-RateLimit-Reset"), anyString());
        verify(response).setStatus(HttpServletResponse.SC_TOO_MANY_REQUESTS);
        verify(filterChain, never()).doFilter(request, response);
    }
}
