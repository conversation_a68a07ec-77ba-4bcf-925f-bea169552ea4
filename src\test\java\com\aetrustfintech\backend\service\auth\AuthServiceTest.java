package com.aetrustfintech.backend.service.auth;

import com.aetrustfintech.backend.dto.auth.AuthResponse;
import com.aetrustfintech.backend.dto.auth.LoginRequest;
import com.aetrustfintech.backend.dto.auth.RegisterRequest;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.UserRepository;
import com.aetrustfintech.backend.security.JwtUtil;
import com.aetrustfintech.backend.service.user.UserService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Jest-like test structure for AuthService
 * Using JUnit 5 @Nested for describe blocks and @Test for it blocks
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AuthService")
class AuthServiceTest {

    @Mock
    private UserRepository userRepository;
    
    @Mock
    private PasswordEncoder passwordEncoder;
    
    @Mock
    private JwtUtil jwtUtil;
    
    @Mock
    private UserService userService;
    
    @InjectMocks
    private AuthService authService;

    private User testUser;
    private RegisterRequest registerRequest;
    private LoginRequest loginRequest;

    @BeforeEach
    void setUp() {
        // Setup test data
        testUser = new User();
        testUser.setId(UUID.randomUUID());
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("hashedPassword");
        testUser.setFirstName("John");
        testUser.setLastName("Doe");
        
        registerRequest = new RegisterRequest();
        registerRequest.setEmail("<EMAIL>");
        registerRequest.setPassword("Password123!");
        registerRequest.setFirstName("John");
        registerRequest.setLastName("Doe");
        registerRequest.setPhone("+1234567890");
        
        loginRequest = new LoginRequest();
        loginRequest.setEmail("<EMAIL>");
        loginRequest.setPassword("Password123!");
    }

    @Nested
    @DisplayName("register")
    class RegisterTests {

        @Test
        @DisplayName("should register user successfully")
        void shouldRegisterUserSuccessfully() {
            // Arrange
            when(userRepository.existsByEmail(registerRequest.getEmail())).thenReturn(false);
            when(passwordEncoder.encode(registerRequest.getPassword())).thenReturn("hashedPassword");
            when(userRepository.save(any(User.class))).thenReturn(testUser);
            when(userService.convertToResponse(testUser)).thenReturn(any());

            // Act
            AuthResponse response = authService.register(registerRequest);

            // Assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertEquals("Registration successful", response.getMessage());
            verify(userRepository).save(any(User.class));
        }

        @Test
        @DisplayName("should throw exception when email already exists")
        void shouldThrowExceptionWhenEmailExists() {
            // Arrange
            when(userRepository.existsByEmail(registerRequest.getEmail())).thenReturn(true);

            // Act & Assert
            assertThrows(RuntimeException.class, () -> authService.register(registerRequest));
            verify(userRepository, never()).save(any(User.class));
        }
    }

    @Nested
    @DisplayName("login")
    class LoginTests {

        @Test
        @DisplayName("should login user successfully")
        void shouldLoginUserSuccessfully() {
            // Arrange
            when(userRepository.findByEmail(loginRequest.getEmail())).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches(loginRequest.getPassword(), testUser.getPassword())).thenReturn(true);
            when(jwtUtil.generateToken(testUser.getEmail())).thenReturn("jwt-token");
            when(userService.convertToResponse(testUser)).thenReturn(any());

            // Act
            AuthResponse response = authService.login(loginRequest);

            // Assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertEquals("Login successful", response.getMessage());
            assertNotNull(response.getAccessToken());
        }

        @Test
        @DisplayName("should throw exception when user not found")
        void shouldThrowExceptionWhenUserNotFound() {
            // Arrange
            when(userRepository.findByEmail(loginRequest.getEmail())).thenReturn(Optional.empty());

            // Act & Assert
            assertThrows(RuntimeException.class, () -> authService.login(loginRequest));
        }

        @Test
        @DisplayName("should throw exception when password is invalid")
        void shouldThrowExceptionWhenPasswordInvalid() {
            // Arrange
            when(userRepository.findByEmail(loginRequest.getEmail())).thenReturn(Optional.of(testUser));
            when(passwordEncoder.matches(loginRequest.getPassword(), testUser.getPassword())).thenReturn(false);

            // Act & Assert
            assertThrows(RuntimeException.class, () -> authService.login(loginRequest));
        }
    }

    @Nested
    @DisplayName("validateToken")
    class ValidateTokenTests {

        @Test
        @DisplayName("should validate token successfully")
        void shouldValidateTokenSuccessfully() {
            // Arrange
            String token = "valid-jwt-token";
            when(jwtUtil.validateToken(token)).thenReturn(true);
            when(jwtUtil.extractUsername(token)).thenReturn("<EMAIL>");
            when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(testUser));

            // Act
            boolean isValid = authService.validateToken(token);

            // Assert
            assertTrue(isValid);
        }

        @Test
        @DisplayName("should return false for invalid token")
        void shouldReturnFalseForInvalidToken() {
            // Arrange
            String token = "invalid-jwt-token";
            when(jwtUtil.validateToken(token)).thenReturn(false);

            // Act
            boolean isValid = authService.validateToken(token);

            // Assert
            assertFalse(isValid);
        }
    }

    @Nested
    @DisplayName("refreshToken")
    class RefreshTokenTests {

        @Test
        @DisplayName("should refresh token successfully")
        void shouldRefreshTokenSuccessfully() {
            // Arrange
            String refreshToken = "valid-refresh-token";
            when(jwtUtil.validateToken(refreshToken)).thenReturn(true);
            when(jwtUtil.extractUsername(refreshToken)).thenReturn("<EMAIL>");
            when(userRepository.findByEmail("<EMAIL>")).thenReturn(Optional.of(testUser));
            when(jwtUtil.generateToken("<EMAIL>")).thenReturn("new-jwt-token");

            // Act
            AuthResponse response = authService.refreshToken(refreshToken);

            // Assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertEquals("Token refreshed successfully", response.getMessage());
            assertNotNull(response.getAccessToken());
        }

        @Test
        @DisplayName("should throw exception for invalid refresh token")
        void shouldThrowExceptionForInvalidRefreshToken() {
            // Arrange
            String refreshToken = "invalid-refresh-token";
            when(jwtUtil.validateToken(refreshToken)).thenReturn(false);

            // Act & Assert
            assertThrows(RuntimeException.class, () -> authService.refreshToken(refreshToken));
        }
    }

    @Nested
    @DisplayName("logout")
    class LogoutTests {

        @Test
        @DisplayName("should logout user successfully")
        void shouldLogoutUserSuccessfully() {
            // Arrange
            String token = "valid-jwt-token";

            // Act
            AuthResponse response = authService.logout(token);

            // Assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertEquals("Logout successful", response.getMessage());
        }
    }
}
