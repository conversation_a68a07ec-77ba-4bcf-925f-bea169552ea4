package com.aetrustfintech.backend.service.auth;

import com.aetrustfintech.backend.dto.auth.OtpRequest;
import com.aetrustfintech.backend.model.OtpVerification;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.OtpVerificationRepository;
import com.aetrustfintech.backend.repository.UserRepository;
import com.aetrustfintech.backend.service.notification.EmailService;
import com.aetrustfintech.backend.service.notification.SmsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Jest-like test structure for OtpService
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("OtpService")
class OtpServiceTest {

    @Mock
    private OtpVerificationRepository otpRepository;
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private EmailService emailService;
    
    @Mock
    private SmsService smsService;
    
    @InjectMocks
    private OtpService otpService;

    private User testUser;
    private OtpRequest otpRequest;
    private OtpVerification otpVerification;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(UUID.randomUUID());
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("+1234567890");
        
        otpRequest = new OtpRequest();
        otpRequest.setIdentifier("<EMAIL>");
        otpRequest.setOtpCode("123456");
        otpRequest.setOtpType("EMAIL_VERIFICATION");
        
        otpVerification = new OtpVerification();
        otpVerification.setId(1L);
        otpVerification.setIdentifier("<EMAIL>");
        otpVerification.setOtpCode("123456");
        otpVerification.setOtpType("EMAIL_VERIFICATION");
        otpVerification.setExpiresAt(LocalDateTime.now().plusMinutes(10));
        otpVerification.setAttempts(0);
    }

    @Nested
    @DisplayName("sendOtp")
    class SendOtpTests {

        @Test
        @DisplayName("should send email OTP successfully")
        void shouldSendEmailOtpSuccessfully() {
            // Arrange
            String identifier = "<EMAIL>";
            String otpType = "EMAIL_VERIFICATION";
            
            when(otpRepository.countOtpRequestsSince(eq(identifier), any(LocalDateTime.class))).thenReturn(0L);
            when(otpRepository.findLatestPendingOtp(identifier, otpType)).thenReturn(Optional.empty());
            when(otpRepository.save(any(OtpVerification.class))).thenReturn(otpVerification);
            doNothing().when(emailService).sendOtpEmail(eq(identifier), anyString(), eq(otpType));

            // Act
            assertDoesNotThrow(() -> otpService.sendOtp(identifier, otpType));

            // Assert
            verify(otpRepository).save(any(OtpVerification.class));
            verify(emailService).sendOtpEmail(eq(identifier), anyString(), eq(otpType));
        }

        @Test
        @DisplayName("should send SMS OTP successfully")
        void shouldSendSmsOtpSuccessfully() {
            // Arrange
            String identifier = "+1234567890";
            String otpType = "PHONE_VERIFICATION";
            
            when(otpRepository.countOtpRequestsSince(eq(identifier), any(LocalDateTime.class))).thenReturn(0L);
            when(otpRepository.findLatestPendingOtp(identifier, otpType)).thenReturn(Optional.empty());
            when(otpRepository.save(any(OtpVerification.class))).thenReturn(otpVerification);
            doNothing().when(smsService).sendOtpSms(eq(identifier), anyString(), eq(otpType));

            // Act
            assertDoesNotThrow(() -> otpService.sendOtp(identifier, otpType));

            // Assert
            verify(otpRepository).save(any(OtpVerification.class));
            verify(smsService).sendOtpSms(eq(identifier), anyString(), eq(otpType));
        }

        @Test
        @DisplayName("should throw exception when too many requests")
        void shouldThrowExceptionWhenTooManyRequests() {
            // Arrange
            String identifier = "<EMAIL>";
            String otpType = "EMAIL_VERIFICATION";
            
            when(otpRepository.countOtpRequestsSince(eq(identifier), any(LocalDateTime.class))).thenReturn(6L);

            // Act & Assert
            assertThrows(RuntimeException.class, () -> otpService.sendOtp(identifier, otpType));
            verify(otpRepository, never()).save(any(OtpVerification.class));
        }

        @Test
        @DisplayName("should expire existing pending OTP before sending new one")
        void shouldExpireExistingPendingOtp() {
            // Arrange
            String identifier = "<EMAIL>";
            String otpType = "EMAIL_VERIFICATION";
            
            OtpVerification existingOtp = new OtpVerification();
            existingOtp.setId(1L);
            
            when(otpRepository.countOtpRequestsSince(eq(identifier), any(LocalDateTime.class))).thenReturn(0L);
            when(otpRepository.findLatestPendingOtp(identifier, otpType)).thenReturn(Optional.of(existingOtp));
            when(otpRepository.save(any(OtpVerification.class))).thenReturn(otpVerification);
            doNothing().when(emailService).sendOtpEmail(eq(identifier), anyString(), eq(otpType));

            // Act
            assertDoesNotThrow(() -> otpService.sendOtp(identifier, otpType));

            // Assert
            verify(otpRepository, times(2)).save(any(OtpVerification.class)); // Once for expiring, once for new OTP
        }
    }

    @Nested
    @DisplayName("verifyOtp")
    class VerifyOtpTests {

        @Test
        @DisplayName("should verify OTP successfully")
        void shouldVerifyOtpSuccessfully() {
            // Arrange
            when(otpRepository.findLatestPendingOtp(otpRequest.getIdentifier(), otpRequest.getOtpType()))
                .thenReturn(Optional.of(otpVerification));
            when(userRepository.findByEmail(otpRequest.getIdentifier())).thenReturn(Optional.of(testUser));
            when(otpRepository.save(any(OtpVerification.class))).thenReturn(otpVerification);
            when(userRepository.save(any(User.class))).thenReturn(testUser);

            // Act
            boolean result = otpService.verifyOtp(otpRequest);

            // Assert
            assertTrue(result);
            verify(otpRepository).save(any(OtpVerification.class));
            verify(userRepository).save(any(User.class));
        }

        @Test
        @DisplayName("should return false for invalid OTP code")
        void shouldReturnFalseForInvalidOtpCode() {
            // Arrange
            otpVerification.setOtpCode("654321"); // Different from request
            when(otpRepository.findLatestPendingOtp(otpRequest.getIdentifier(), otpRequest.getOtpType()))
                .thenReturn(Optional.of(otpVerification));

            // Act
            boolean result = otpService.verifyOtp(otpRequest);

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("should return false when OTP not found")
        void shouldReturnFalseWhenOtpNotFound() {
            // Arrange
            when(otpRepository.findLatestPendingOtp(otpRequest.getIdentifier(), otpRequest.getOtpType()))
                .thenReturn(Optional.empty());

            // Act
            boolean result = otpService.verifyOtp(otpRequest);

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("should return false when OTP is expired")
        void shouldReturnFalseWhenOtpExpired() {
            // Arrange
            otpVerification.setExpiresAt(LocalDateTime.now().minusMinutes(1)); // Expired
            when(otpRepository.findLatestPendingOtp(otpRequest.getIdentifier(), otpRequest.getOtpType()))
                .thenReturn(Optional.of(otpVerification));

            // Act
            boolean result = otpService.verifyOtp(otpRequest);

            // Assert
            assertFalse(result);
        }

        @Test
        @DisplayName("should return false when max attempts exceeded")
        void shouldReturnFalseWhenMaxAttemptsExceeded() {
            // Arrange
            otpVerification.setAttempts(5); // Max attempts reached
            when(otpRepository.findLatestPendingOtp(otpRequest.getIdentifier(), otpRequest.getOtpType()))
                .thenReturn(Optional.of(otpVerification));

            // Act
            boolean result = otpService.verifyOtp(otpRequest);

            // Assert
            assertFalse(result);
        }
    }

    @Nested
    @DisplayName("isEmail")
    class IsEmailTests {

        @Test
        @DisplayName("should return true for valid email")
        void shouldReturnTrueForValidEmail() {
            // Act & Assert
            assertTrue(otpService.isEmail("<EMAIL>"));
            assertTrue(otpService.isEmail("<EMAIL>"));
        }

        @Test
        @DisplayName("should return false for phone number")
        void shouldReturnFalseForPhoneNumber() {
            // Act & Assert
            assertFalse(otpService.isEmail("+1234567890"));
            assertFalse(otpService.isEmail("1234567890"));
        }

        @Test
        @DisplayName("should return false for invalid email")
        void shouldReturnFalseForInvalidEmail() {
            // Act & Assert
            assertFalse(otpService.isEmail("invalid-email"));
            assertFalse(otpService.isEmail("@domain.com"));
            assertFalse(otpService.isEmail("user@"));
        }
    }
}
