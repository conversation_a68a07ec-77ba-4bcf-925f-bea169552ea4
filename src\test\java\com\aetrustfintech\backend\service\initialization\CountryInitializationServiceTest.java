package com.aetrustfintech.backend.service.initialization;

import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.UserRepository;
import com.aetrustfintech.backend.service.notification.EmailService;
import com.aetrustfintech.backend.service.notification.SmsService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CountryInitializationServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private EmailService emailService;

    @Mock
    private SmsService smsService;

    @InjectMocks
    private CountryInitializationService countryInitService;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(UUID.randomUUID());
        testUser.setEmail("<EMAIL>");
        testUser.setPhone("+************");
        testUser.setFirstName("John");
        testUser.setLastName("Doe");
    }

    @Test
    void initializeUserForCountry_Ethiopia_Success() {
        // Arrange
        testUser.setCountryCode("ET");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // Act
        assertDoesNotThrow(() -> countryInitService.initializeUserForCountry(testUser));

        // Assert
        verify(userRepository).save(testUser);
        verify(emailService).sendEmail(eq(testUser.getEmail()), eq("Welcome to AeTrust"), anyString());
        verify(smsService).sendSms(eq(testUser.getPhone()), contains("Welcome to AeTrust"));
        
        assertNotNull(testUser.getInitializedAt());
        assertNotNull(testUser.getCountrySpecificData());
        assertTrue(testUser.getCountrySpecificData().contains("\"twoFaThreshold\":1000"));
        assertTrue(testUser.getCountrySpecificData().contains("\"currency\":\"ETB\""));
        assertTrue(testUser.getCountrySpecificData().contains("\"dataLocalization\":true"));
        assertTrue(testUser.getCountrySpecificData().contains("\"peerIntroRequired\":true"));
    }

    @Test
    void initializeUserForCountry_Rwanda_Success() {
        // Arrange
        testUser.setCountryCode("RW");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // Act
        assertDoesNotThrow(() -> countryInitService.initializeUserForCountry(testUser));

        // Assert
        verify(userRepository).save(testUser);
        verify(emailService).sendEmail(eq(testUser.getEmail()), eq("Welcome to AeTrust"), anyString());
        verify(smsService).sendSms(eq(testUser.getPhone()), contains("Welcome to AeTrust"));
        
        assertNotNull(testUser.getInitializedAt());
        assertNotNull(testUser.getCountrySpecificData());
        assertTrue(testUser.getCountrySpecificData().contains("\"twoFaThreshold\":100000"));
        assertTrue(testUser.getCountrySpecificData().contains("\"currency\":\"RWF\""));
        assertTrue(testUser.getCountrySpecificData().contains("\"gdprCompliance\":true"));
        assertTrue(testUser.getCountrySpecificData().contains("\"nidaRequired\":true"));
    }

    @Test
    void initializeUserForCountry_DefaultCountry_Success() {
        // Arrange
        testUser.setCountryCode("US");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // Act
        assertDoesNotThrow(() -> countryInitService.initializeUserForCountry(testUser));

        // Assert
        verify(userRepository).save(testUser);
        verify(emailService).sendEmail(eq(testUser.getEmail()), eq("Welcome to AeTrust"), anyString());
        verify(smsService).sendSms(eq(testUser.getPhone()), contains("Welcome to AeTrust"));
        
        assertNotNull(testUser.getInitializedAt());
        assertNotNull(testUser.getCountrySpecificData());
        assertTrue(testUser.getCountrySpecificData().contains("\"twoFaThreshold\":50000"));
        assertTrue(testUser.getCountrySpecificData().contains("\"currency\":\"USD\""));
        assertTrue(testUser.getCountrySpecificData().contains("\"defaultSettings\":true"));
    }

    @Test
    void initializeUserForCountry_EmailServiceFailure() {
        // Arrange
        testUser.setCountryCode("ET");
        when(userRepository.save(any(User.class))).thenReturn(testUser);
        doThrow(new RuntimeException("Email service unavailable"))
            .when(emailService).sendEmail(anyString(), anyString(), anyString());

        // Act - Should not throw exception even if email fails
        assertDoesNotThrow(() -> countryInitService.initializeUserForCountry(testUser));

        // Assert - User should still be initialized
        verify(userRepository).save(testUser);
        verify(emailService).sendEmail(anyString(), anyString(), anyString());
        verify(smsService).sendSms(anyString(), anyString()); // SMS should still be attempted
        
        assertNotNull(testUser.getInitializedAt());
        assertNotNull(testUser.getCountrySpecificData());
    }

    @Test
    void initializeUserForCountry_SmsServiceFailure() {
        // Arrange
        testUser.setCountryCode("RW");
        when(userRepository.save(any(User.class))).thenReturn(testUser);
        doThrow(new RuntimeException("SMS service unavailable"))
            .when(smsService).sendSms(anyString(), anyString());

        // Act - Should not throw exception even if SMS fails
        assertDoesNotThrow(() -> countryInitService.initializeUserForCountry(testUser));

        // Assert - User should still be initialized
        verify(userRepository).save(testUser);
        verify(emailService).sendEmail(anyString(), anyString(), anyString()); // Email should still work
        verify(smsService).sendSms(anyString(), anyString());
        
        assertNotNull(testUser.getInitializedAt());
        assertNotNull(testUser.getCountrySpecificData());
    }

    @Test
    void initializeUserForCountry_DatabaseFailure() {
        // Arrange
        testUser.setCountryCode("ET");
        when(userRepository.save(any(User.class))).thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> countryInitService.initializeUserForCountry(testUser)
        );

        assertEquals("Auth initialization failed", exception.getMessage());
        verify(userRepository).save(testUser);
        // Notifications should not be sent if database save fails
        verify(emailService, never()).sendEmail(anyString(), anyString(), anyString());
        verify(smsService, never()).sendSms(anyString(), anyString());
    }

    @Test
    void initializeUserForCountry_InvalidCountryCode() {
        // Arrange
        testUser.setCountryCode("INVALID");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // Act & Assert
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> countryInitService.initializeUserForCountry(testUser)
        );

        assertEquals("Auth initialization failed", exception.getMessage());
        assertTrue(exception.getCause().getMessage().contains("Unknown country code"));
    }

    @Test
    void initializeUserForCountry_NullCountryCode() {
        // Arrange
        testUser.setCountryCode(null);

        // Act & Assert
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> countryInitService.initializeUserForCountry(testUser)
        );

        assertEquals("Auth initialization failed", exception.getMessage());
    }

    @Test
    void initializeUserForCountry_WelcomeMessageContent_Ethiopia() {
        // Arrange
        testUser.setCountryCode("ET");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // Act
        countryInitService.initializeUserForCountry(testUser);

        // Assert - Verify Ethiopia-specific content in welcome message
        verify(emailService).sendEmail(
            eq(testUser.getEmail()),
            eq("Welcome to AeTrust"),
            contains("Ethiopian user")
        );
        verify(emailService).sendEmail(
            eq(testUser.getEmail()),
            eq("Welcome to AeTrust"),
            contains("NBE regulations")
        );
        verify(emailService).sendEmail(
            eq(testUser.getEmail()),
            eq("Welcome to AeTrust"),
            contains("Level 1")
        );
    }

    @Test
    void initializeUserForCountry_WelcomeMessageContent_Rwanda() {
        // Arrange
        testUser.setCountryCode("RW");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // Act
        countryInitService.initializeUserForCountry(testUser);

        // Assert - Verify Rwanda-specific content in welcome message
        verify(emailService).sendEmail(
            eq(testUser.getEmail()),
            eq("Welcome to AeTrust"),
            contains("Rwandan user")
        );
        verify(emailService).sendEmail(
            eq(testUser.getEmail()),
            eq("Welcome to AeTrust"),
            contains("NIDA-verified")
        );
        verify(emailService).sendEmail(
            eq(testUser.getEmail()),
            eq("Welcome to AeTrust"),
            contains("BNR-regulated")
        );
    }
}
