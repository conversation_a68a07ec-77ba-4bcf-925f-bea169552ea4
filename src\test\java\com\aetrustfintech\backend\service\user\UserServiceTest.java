package com.aetrustfintech.backend.service.user;

import com.aetrustfintech.backend.dto.auth.UpdatePasswordRequest;
import com.aetrustfintech.backend.exception.ResourceNotFoundException;
import com.aetrustfintech.backend.exception.ValidationException;
import com.aetrustfintech.backend.model.User;
import com.aetrustfintech.backend.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @InjectMocks
    private UserService userService;

    private User testUser;
    private UUID userId;

    @BeforeEach
    void setUp() {
        userId = UUID.randomUUID();
        testUser = new User();
        testUser.setId(userId);
        testUser.setEmail("<EMAIL>");
        testUser.setPassword("$2a$10$encodedCurrentPassword");
        testUser.setPasswordChangedAt(LocalDateTime.now().minusDays(30));
    }

    @Test
    void updatePassword_Success() {
        // Arrange
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setCurrentPassword("currentPassword123!");
        request.setNewPassword("newPassword456@");
        request.setConfirmPassword("newPassword456@");

        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("currentPassword123!", testUser.getPassword())).thenReturn(true);
        when(passwordEncoder.matches("newPassword456@", testUser.getPassword())).thenReturn(false);
        when(passwordEncoder.encode("newPassword456@")).thenReturn("$2a$10$encodedNewPassword");
        when(userRepository.save(any(User.class))).thenReturn(testUser);

        // Act
        assertDoesNotThrow(() -> userService.updatePassword(userId, request));

        // Assert
        verify(userRepository).findById(userId);
        verify(passwordEncoder).matches("currentPassword123!", testUser.getPassword());
        verify(passwordEncoder).matches("newPassword456@", testUser.getPassword());
        verify(passwordEncoder).encode("newPassword456@");
        verify(userRepository).save(testUser);
        
        assertEquals("$2a$10$encodedNewPassword", testUser.getPassword());
        assertNotNull(testUser.getPasswordChangedAt());
    }

    @Test
    void updatePassword_UserNotFound() {
        // Arrange
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setCurrentPassword("currentPassword123!");
        request.setNewPassword("newPassword456@");
        request.setConfirmPassword("newPassword456@");

        when(userRepository.findById(userId)).thenReturn(Optional.empty());

        // Act & Assert
        ResourceNotFoundException exception = assertThrows(
            ResourceNotFoundException.class,
            () -> userService.updatePassword(userId, request)
        );

        assertEquals("User not found with ID: " + userId, exception.getMessage());
        verify(userRepository).findById(userId);
        verifyNoMoreInteractions(passwordEncoder, userRepository);
    }

    @Test
    void updatePassword_IncorrectCurrentPassword() {
        // Arrange
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setCurrentPassword("wrongPassword");
        request.setNewPassword("newPassword456@");
        request.setConfirmPassword("newPassword456@");

        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("wrongPassword", testUser.getPassword())).thenReturn(false);

        // Act & Assert
        ValidationException exception = assertThrows(
            ValidationException.class,
            () -> userService.updatePassword(userId, request)
        );

        assertEquals("Current password is incorrect", exception.getMessage());
        verify(userRepository).findById(userId);
        verify(passwordEncoder).matches("wrongPassword", testUser.getPassword());
        verify(userRepository, never()).save(any());
    }

    @Test
    void updatePassword_SameAsCurrentPassword() {
        // Arrange
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setCurrentPassword("currentPassword123!");
        request.setNewPassword("currentPassword123!");
        request.setConfirmPassword("currentPassword123!");

        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("currentPassword123!", testUser.getPassword())).thenReturn(true);

        // Act & Assert
        ValidationException exception = assertThrows(
            ValidationException.class,
            () -> userService.updatePassword(userId, request)
        );

        assertEquals("New password must be different from current password", exception.getMessage());
        verify(userRepository).findById(userId);
        verify(passwordEncoder, times(2)).matches("currentPassword123!", testUser.getPassword());
        verify(userRepository, never()).save(any());
    }

    @Test
    void updatePassword_ValidationFailure() {
        // This test would be handled by the validation annotations on UpdatePasswordRequest
        // The validation happens at the controller level before reaching the service
        
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setCurrentPassword("current");
        request.setNewPassword("weak"); // Too weak password
        request.setConfirmPassword("different"); // Different confirmation

        // In a real scenario, this would be caught by @Valid annotation
        // Here we're testing that the service assumes valid input
        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("current", testUser.getPassword())).thenReturn(true);
        when(passwordEncoder.matches("weak", testUser.getPassword())).thenReturn(false);
        when(passwordEncoder.encode("weak")).thenReturn("$2a$10$encodedWeakPassword");

        // Act
        assertDoesNotThrow(() -> userService.updatePassword(userId, request));

        // Assert - Service trusts that validation happened at controller level
        verify(userRepository).save(testUser);
    }

    @Test
    void updatePassword_DatabaseError() {
        // Arrange
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setCurrentPassword("currentPassword123!");
        request.setNewPassword("newPassword456@");
        request.setConfirmPassword("newPassword456@");

        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("currentPassword123!", testUser.getPassword())).thenReturn(true);
        when(passwordEncoder.matches("newPassword456@", testUser.getPassword())).thenReturn(false);
        when(passwordEncoder.encode("newPassword456@")).thenReturn("$2a$10$encodedNewPassword");
        when(userRepository.save(any(User.class))).thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> userService.updatePassword(userId, request)
        );

        assertEquals("Database connection failed", exception.getMessage());
        verify(userRepository).save(testUser);
    }

    @Test
    void updatePassword_PasswordEncodingError() {
        // Arrange
        UpdatePasswordRequest request = new UpdatePasswordRequest();
        request.setCurrentPassword("currentPassword123!");
        request.setNewPassword("newPassword456@");
        request.setConfirmPassword("newPassword456@");

        when(userRepository.findById(userId)).thenReturn(Optional.of(testUser));
        when(passwordEncoder.matches("currentPassword123!", testUser.getPassword())).thenReturn(true);
        when(passwordEncoder.matches("newPassword456@", testUser.getPassword())).thenReturn(false);
        when(passwordEncoder.encode("newPassword456@")).thenThrow(new RuntimeException("Encoding failed"));

        // Act & Assert
        RuntimeException exception = assertThrows(
            RuntimeException.class,
            () -> userService.updatePassword(userId, request)
        );

        assertEquals("Encoding failed", exception.getMessage());
        verify(passwordEncoder).encode("newPassword456@");
        verify(userRepository, never()).save(any());
    }
}
